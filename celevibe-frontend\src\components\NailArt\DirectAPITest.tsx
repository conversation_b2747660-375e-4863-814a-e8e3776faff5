import React, { useState } from 'react';

const DirectAPITest: React.FC = () => {
  const [result, setResult] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  const testFreepikAPI = async () => {
    setIsLoading(true);
    setResult('Testing Freepik API...');

    const requestBody = {
      prompt: "Professional photograph of elegant female hands with dark burgundy nail polish, glossy finish, perfect manicure, oval shaped nails, light skin tone, clean white background, studio lighting, photorealistic",
      resolution: "2k",
      aspect_ratio: "classic_4_3",
      model: "realism",
      creative_detailing: 50,
      engine: "magnific_sharpy",
      fixed_generation: false,
      filter_nsfw: true
    };

    try {
      console.log('Making direct API call to Freepik...');
      
      const response = await fetch('https://api.freepik.com/v1/ai/mystic', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-freepik-api-key': 'FPSX7f1836a11ecbc6965b7c81275c729e55'
        },
        body: JSON.stringify(requestBody)
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', [...response.headers.entries()]);

      if (!response.ok) {
        const errorText = await response.text();
        setResult(`❌ API Error (${response.status}): ${errorText}`);
        return;
      }

      const data = await response.json();
      console.log('API Response:', data);
      setResult(`✅ Success! Task ID: ${data.task_id}\nStatus: ${data.task_status}\nResponse: ${JSON.stringify(data, null, 2)}`);

    } catch (error) {
      console.error('API Test Error:', error);
      setResult(`❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div style={{
      background: 'rgba(255, 255, 255, 0.9)',
      borderRadius: '1rem',
      padding: '2rem',
      margin: '2rem 0',
      border: '2px solid #3b82f6'
    }}>
      <h3 style={{
        fontSize: '1.25rem',
        fontWeight: '600',
        color: '#1e40af',
        marginBottom: '1rem'
      }}>
        🧪 Direct Freepik API Test
      </h3>

      <button
        onClick={testFreepikAPI}
        disabled={isLoading}
        style={{
          padding: '1rem 2rem',
          background: isLoading ? '#9ca3af' : '#3b82f6',
          color: 'white',
          border: 'none',
          borderRadius: '0.5rem',
          fontWeight: '600',
          cursor: isLoading ? 'not-allowed' : 'pointer',
          marginBottom: '1rem'
        }}
      >
        {isLoading ? 'Testing...' : 'Test Freepik API'}
      </button>

      <div style={{
        background: '#f8fafc',
        border: '1px solid #e2e8f0',
        borderRadius: '0.5rem',
        padding: '1rem',
        fontFamily: 'monospace',
        fontSize: '0.875rem',
        whiteSpace: 'pre-wrap',
        maxHeight: '300px',
        overflow: 'auto'
      }}>
        {result || 'Click the button to test the API...'}
      </div>

      <div style={{
        marginTop: '1rem',
        padding: '1rem',
        background: 'rgba(59, 130, 246, 0.1)',
        borderRadius: '0.5rem',
        fontSize: '0.875rem',
        color: '#1e40af'
      }}>
        <strong>API Details:</strong><br />
        • Endpoint: https://api.freepik.com/v1/ai/mystic<br />
        • API Key: FPSX7f1836a11ecbc6965b7c81275c729e55<br />
        • Method: POST<br />
        • Expected: Task ID and status response
      </div>
    </div>
  );
};

export default DirectAPITest;
