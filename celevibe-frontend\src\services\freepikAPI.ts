// Freepik Mystic API Integration for AI Image Generation

// Use backend proxy to avoid CORS issues
const BACKEND_BASE_URL = 'http://localhost:9001';
const FREEPIK_API_KEY = 'FPSX7f1836a11ecbc6965b7c81275c729e55';
const FREEPIK_BASE_URL = 'https://api.freepik.com/v1/ai/mystic';

export interface ImageGenerationRequest {
  prompt: string;
  resolution?: '1k' | '2k' | '4k';
  aspect_ratio?: string;
  model?: 'realism' | 'fluid' | 'zen';
  creative_detailing?: number;
  engine?: 'automatic' | 'magnific_illusio' | 'magnific_sharpy' | 'magnific_sparkle';
  style_reference?: string; // Base64 image
  structure_reference?: string; // Base64 image
  adherence?: number;
  hdr?: number;
  structure_strength?: number;
}

export interface ImageGenerationResponse {
  generated: string[];
  task_id: string;
  task_status: 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
}

export interface TaskStatusResponse {
  data: {
    generated: string[];
    task_id: string;
    status: 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
    has_nsfw?: boolean[];
  };
}

// Generate nail art hand images using Freepik Mystic API
export const generateNailArtImage = async (
  nailArtStyle: string,
  skinTone: string = 'light',
  nailShape: string = 'oval'
): Promise<ImageGenerationResponse> => {
  const prompt = `Professional photograph of elegant female hands with ${nailArtStyle} nail art, ${skinTone} skin tone, ${nailShape} shaped nails, perfect manicure, high-quality studio lighting, clean white background, photorealistic, ultra-detailed, 8k resolution, beauty photography style`;

  const requestBody = {
    prompt,
    resolution: '2k' as const,
    aspect_ratio: 'classic_4_3',
    model: 'realism' as const,
    creative_detailing: 45,
    engine: 'magnific_sharpy' as const,
    fixed_generation: false,
    filter_nsfw: true
  };

  try {
    console.log('Making API request via backend proxy');
    console.log('Request body:', JSON.stringify(requestBody, null, 2));

    const response = await fetch(`${BACKEND_BASE_URL}/api/generate-nail-art`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', response.headers);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('API Error Response:', errorText);
      throw new Error(`API request failed: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const data = await response.json();
    console.log('API Response:', data);
    return data;
  } catch (error) {
    console.error('Error generating nail art image:', error);
    if (error instanceof TypeError && error.message.includes('fetch')) {
      throw new Error('Network error: Unable to connect to Freepik API. This might be due to CORS restrictions.');
    }
    throw error;
  }
};

// Generate specific burgundy nail art like the user's image
export const generateBurgundyNailArt = async (): Promise<ImageGenerationResponse> => {
  try {
    console.log('Generating burgundy nail art via backend...');

    const response = await fetch(`${BACKEND_BASE_URL}/api/generate-burgundy-nails`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Backend API error: ${errorData.error} - ${errorData.details}`);
    }

    const data = await response.json();
    console.log('Burgundy nail generation response:', data);
    return data;
  } catch (error) {
    console.error('Error generating burgundy nail art:', error);
    throw error;
  }
};

// Check the status of a generation task
export const checkTaskStatus = async (taskId: string): Promise<TaskStatusResponse> => {
  try {
    console.log('Checking task status via backend:', taskId);

    const response = await fetch(`${BACKEND_BASE_URL}/api/task-status/${taskId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('Task status response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Task status error response:', errorText);
      let errorData;
      try {
        errorData = JSON.parse(errorText);
      } catch {
        errorData = { error: errorText };
      }
      throw new Error(`Task status check failed: ${errorData.error || errorText}`);
    }

    const data = await response.json();
    console.log('Task status response data:', JSON.stringify(data, null, 2));
    return data;
  } catch (error) {
    console.error('Error checking task status:', error);
    throw error;
  }
};

// Generate custom nail art based on pattern
export const generateCustomNailArt = async (
  patternName: string,
  colors: string[],
  description: string
): Promise<ImageGenerationResponse> => {
  const colorDescriptions = colors.map(color => {
    // Convert hex to color names for better AI understanding
    const colorMap: { [key: string]: string } = {
      '#FFB6C1': 'light pink',
      '#FFFFFF': 'white',
      '#F8F8FF': 'ghost white',
      '#FF6B6B': 'coral red',
      '#FFE66D': 'golden yellow',
      '#FF8E53': 'orange',
      '#98FB98': 'pale green',
      '#FFD700': 'gold',
      '#000000': 'black',
      '#C0C0C0': 'silver',
      '#4A1A3D': 'dark burgundy',
      '#6B2C5A': 'deep purple'
    };
    return colorMap[color] || color;
  }).join(' and ');

  const prompt = `Professional photograph of elegant female hands with ${patternName.toLowerCase()} nail art design, using ${colorDescriptions} colors, ${description}, perfect manicure, oval shaped nails, light skin tone, clean white background, studio lighting, photorealistic, ultra-detailed, beauty photography, 8k resolution`;

  const requestBody = {
    prompt,
    resolution: '2k' as const,
    aspect_ratio: 'classic_4_3',
    model: 'realism' as const,
    creative_detailing: 40,
    engine: 'magnific_sharpy' as const,
    fixed_generation: false,
    filter_nsfw: true,
    styling: {
      colors: colors.map(color => ({
        color,
        weight: 0.6
      }))
    }
  };

  try {
    console.log('Generating custom nail art via backend...');

    const response = await fetch(`${BACKEND_BASE_URL}/api/generate-nail-art`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Backend API error: ${errorData.error} - ${errorData.details}`);
    }

    const data = await response.json();
    console.log('Custom nail art generation response:', data);
    return data;
  } catch (error) {
    console.error('Error generating custom nail art:', error);
    throw error;
  }
};

// Poll for task completion
export const waitForTaskCompletion = async (
  taskId: string,
  maxAttempts: number = 30,
  intervalMs: number = 2000
): Promise<string[]> => {
  console.log(`Starting to wait for task completion: ${taskId}`);

  for (let attempt = 0; attempt < maxAttempts; attempt++) {
    try {
      console.log(`Attempt ${attempt + 1}/${maxAttempts} - Checking task status...`);
      const statusResponse = await checkTaskStatus(taskId);

      console.log('Status response:', statusResponse);

      if (statusResponse.data && statusResponse.data.status === 'COMPLETED') {
        console.log('Task completed! Generated images:', statusResponse.data.generated);
        return statusResponse.data.generated;
      } else if (statusResponse.data && statusResponse.data.status === 'FAILED') {
        throw new Error('Image generation failed');
      } else if (statusResponse.data) {
        console.log(`Task status: ${statusResponse.data.status}, waiting...`);
      } else {
        console.error('Invalid response structure:', statusResponse);
        throw new Error('Invalid response structure from task status API');
      }

      // Wait before next check
      await new Promise(resolve => setTimeout(resolve, intervalMs));
    } catch (error) {
      console.error(`Attempt ${attempt + 1} failed:`, error);
      if (attempt === maxAttempts - 1) {
        throw error;
      }
      // Wait a bit before retrying on error
      await new Promise(resolve => setTimeout(resolve, intervalMs));
    }
  }

  throw new Error('Task did not complete within the expected time');
};
