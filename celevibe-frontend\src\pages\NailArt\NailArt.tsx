import React, { useState } from 'react';
import NailUploader from '../../components/NailArt/NailUploader';
import HandSimulator from '../../components/NailArt/HandSimulator';
import HandImageUploader from '../../components/NailArt/HandImageUploader';
import ExactHandDisplay from '../../components/NailArt/ExactHandDisplay';
import AIGeneratedHand from '../../components/NailArt/AIGeneratedHand';
import SimpleNailGenerator from '../../components/NailArt/SimpleNailGenerator';
import NailArtPreview from '../../components/NailArt/NailArtPreview';
import StyleOptions from '../../components/NailArt/StyleOptions';
import PromptBasedGenerator from '../../components/NailArt/PromptBasedGenerator';
import CustomizationPanel from '../../components/Customization/CustomizationPanel';
import { analyzeNailImage, getPredictedPatterns, type NailAnalysis, type AIResponse, type PatternPrediction } from '../../services/nailAI';
import { generateCustomNailArt, waitForTaskCompletion } from '../../services/freepikAPI';

const NailArt: React.FC = () => {
  // Add CSS animation for spinning
  React.useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
      }
    `;
    document.head.appendChild(style);
    return () => document.head.removeChild(style);
  }, []);
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [handImage, setHandImage] = useState<string | null>(null); // User's hand photo for simulation
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysis, setAnalysis] = useState<NailAnalysis | null>(null);
  const [aiResponse, setAiResponse] = useState<AIResponse | null>(null);
  const [selectedPattern, setSelectedPattern] = useState<PatternPrediction | null>(null);
  const [currentStep, setCurrentStep] = useState<'upload' | 'analyzing' | 'results'>('upload');

  // New state for enhanced features
  const [selectedStyle, setSelectedStyle] = useState<string>('');
  const [selectedColors, setSelectedColors] = useState<string[]>([]);
  const [customizationSettings, setCustomizationSettings] = useState<any>({});
  const [generatedImage, setGeneratedImage] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationError, setGenerationError] = useState<string | null>(null);
  const [activeSection, setActiveSection] = useState<'generator' | 'preview' | 'customization'>('generator');

  const handleImageUpload = async (file: File, imageUrl: string) => {
    setUploadedImage(imageUrl);
    setIsAnalyzing(true);
    setCurrentStep('analyzing');

    try {
      // Analyze the uploaded image
      const nailAnalysis = await analyzeNailImage(file);
      setAnalysis(nailAnalysis);

      // Get AI predictions
      const predictions = await getPredictedPatterns(nailAnalysis);
      setAiResponse(predictions);
      setSelectedPattern(predictions.recommendations.bestMatch);
      setCurrentStep('results');
    } catch (error) {
      console.error('Error analyzing image:', error);
      // Handle error state
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handlePatternSelect = (pattern: PatternPrediction) => {
    setSelectedPattern(pattern);
  };

  // New handler functions
  const handleStyleSelect = (style: string) => {
    setSelectedStyle(style);
  };

  const handleColorsSelect = (colors: string[]) => {
    setSelectedColors(colors);
  };

  const handleCustomPromptGenerate = async (prompt: string, colors: string[]) => {
    console.log('Generating with prompt:', prompt, 'colors:', colors);
    setIsGenerating(true);
    setGenerationError(null);
    setGeneratedImage(null);

    try {
      // Generate custom nail art using the prompt and colors
      const response = await generateCustomNailArt(
        prompt,
        colors,
        `Custom nail art: ${prompt}`
      );

      console.log('Generation started, task ID:', response.task_id);

      // Wait for generation to complete
      const images = await waitForTaskCompletion(response.task_id);
      console.log('Generation completed, images:', images);

      if (images && images.length > 0) {
        setGeneratedImage(images[0]);
        console.log('Generated image URL:', images[0]);
      } else {
        setGenerationError('No images were generated');
      }
    } catch (error) {
      console.error('Error generating nail art:', error);
      setGenerationError(error instanceof Error ? error.message : 'Generation failed');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleCustomizationChange = (settings: any) => {
    setCustomizationSettings(settings);
  };

  const resetSession = () => {
    setUploadedImage(null);
    setAnalysis(null);
    setAiResponse(null);
    setSelectedPattern(null);
    setCurrentStep('upload');
    setIsAnalyzing(false);
    setSelectedStyle('');
    setSelectedColors([]);
    setCustomizationSettings({});
  };

  return (
    <div className="pt-16 min-h-screen" style={{ background: 'linear-gradient(135deg, #fdf2f8 0%, #f3e8ff 50%, #e0e7ff 100%)' }}>
      <div className="container" style={{ padding: '2rem 1rem' }}>
        {/* Header */}
        <div className="text-center mb-12">
          <h1 style={{ 
            fontSize: '2.5rem', 
            fontFamily: 'Playfair Display, serif', 
            fontWeight: 'bold', 
            marginBottom: '1rem' 
          }}>
            <span className="gradient-text">AI Nail Art Studio</span>
          </h1>
          <p style={{ 
            fontSize: '1.25rem', 
            color: '#6b7280', 
            maxWidth: '48rem', 
            margin: '0 auto' 
          }}>
            Upload a photo of your nails and let our AI predict the perfect nail art patterns for you
          </p>
        </div>

        {/* Progress indicator */}
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          marginBottom: '3rem',
          gap: '1rem'
        }}>
          {['Upload', 'Analyze', 'Results'].map((step, index) => (
            <div key={step} style={{ display: 'flex', alignItems: 'center' }}>
              <div style={{
                width: '2rem',
                height: '2rem',
                borderRadius: '50%',
                background: index === 0 && currentStep === 'upload' ? '#ec4899' :
                           index === 1 && currentStep === 'analyzing' ? '#ec4899' :
                           index === 2 && currentStep === 'results' ? '#ec4899' :
                           index < (['upload', 'analyzing', 'results'].indexOf(currentStep)) ? '#10b981' : '#d1d5db',
                color: 'white',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '0.875rem',
                fontWeight: 'bold'
              }}>
                {index < (['upload', 'analyzing', 'results'].indexOf(currentStep)) ? '✓' : index + 1}
              </div>
              <span style={{ 
                marginLeft: '0.5rem', 
                fontSize: '0.875rem',
                color: index <= (['upload', 'analyzing', 'results'].indexOf(currentStep)) ? '#374151' : '#9ca3af'
              }}>
                {step}
              </span>
              {index < 2 && (
                <div style={{
                  width: '2rem',
                  height: '2px',
                  background: index < (['upload', 'analyzing', 'results'].indexOf(currentStep)) ? '#10b981' : '#d1d5db',
                  marginLeft: '1rem'
                }} />
              )}
            </div>
          ))}
        </div>

        {/* Main content */}
        <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
          {currentStep === 'upload' && (
            <div style={{ maxWidth: '600px', margin: '0 auto' }}>
              <NailUploader
                onImageUpload={handleImageUpload}
                isProcessing={isAnalyzing}
              />

              {/* Section Navigation */}
              <div style={{
                display: 'flex',
                justifyContent: 'center',
                marginBottom: '2rem',
                background: 'rgba(236, 72, 153, 0.1)',
                borderRadius: '1rem',
                padding: '0.5rem'
              }}>
                {[
                  { id: 'generator', label: '🎨 AI Generator', icon: '🎨' },
                  { id: 'preview', label: '👀 Preview', icon: '👀' },
                  { id: 'customization', label: '⚙️ Customize', icon: '⚙️' }
                ].map((section) => (
                  <button
                    key={section.id}
                    onClick={() => setActiveSection(section.id as any)}
                    style={{
                      flex: 1,
                      padding: '1rem',
                      background: activeSection === section.id ? '#ec4899' : 'transparent',
                      color: activeSection === section.id ? 'white' : '#6b7280',
                      border: 'none',
                      borderRadius: '0.5rem',
                      fontWeight: '600',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      fontSize: '0.875rem'
                    }}
                  >
                    {section.label}
                  </button>
                ))}
              </div>

              {/* Active Section Content */}
              {activeSection === 'generator' && (
                <div>
                  <SimpleNailGenerator />
                  <PromptBasedGenerator
                    onGenerate={handleCustomPromptGenerate}
                    isGenerating={isGenerating}
                  />

                  {/* Generated Image Display */}
                  {(generatedImage || generationError || isGenerating) && (
                    <div style={{
                      marginTop: '2rem',
                      padding: '2rem',
                      background: 'rgba(255, 255, 255, 0.9)',
                      borderRadius: '1.5rem',
                      boxShadow: '0 8px 24px rgba(0, 0, 0, 0.1)',
                      textAlign: 'center'
                    }}>
                      <h3 style={{
                        fontSize: '1.5rem',
                        fontWeight: '600',
                        color: '#374151',
                        marginBottom: '1.5rem',
                        fontFamily: 'Playfair Display, serif'
                      }}>
                        🎨 Your Custom Nail Art
                      </h3>

                      {isGenerating && (
                        <div style={{
                          padding: '2rem',
                          color: '#6b7280'
                        }}>
                          <div style={{
                            fontSize: '3rem',
                            marginBottom: '1rem',
                            animation: 'spin 2s linear infinite'
                          }}>
                            ⏳
                          </div>
                          <p style={{ fontSize: '1.1rem', fontWeight: '500' }}>
                            Generating your custom nail art...
                          </p>
                          <p style={{ fontSize: '0.9rem', marginTop: '0.5rem' }}>
                            This may take 30-60 seconds
                          </p>
                        </div>
                      )}

                      {generationError && (
                        <div style={{
                          padding: '2rem',
                          background: 'rgba(239, 68, 68, 0.1)',
                          borderRadius: '1rem',
                          border: '1px solid rgba(239, 68, 68, 0.2)'
                        }}>
                          <p style={{ color: '#dc2626', marginBottom: '1rem' }}>❌ {generationError}</p>
                          <p style={{ color: '#6b7280', fontSize: '0.875rem' }}>
                            Please try again with a different prompt or check your connection.
                          </p>
                        </div>
                      )}

                      {generatedImage && (
                        <div>
                          <img
                            src={generatedImage}
                            alt="Generated Custom Nail Art"
                            style={{
                              maxWidth: '100%',
                              maxHeight: '500px',
                              borderRadius: '1rem',
                              boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',
                              marginBottom: '1rem'
                            }}
                          />
                          <div style={{
                            display: 'flex',
                            gap: '1rem',
                            justifyContent: 'center',
                            flexWrap: 'wrap'
                          }}>
                            <button
                              onClick={() => {
                                const link = document.createElement('a');
                                link.href = generatedImage;
                                link.download = 'custom-nail-art.jpg';
                                link.click();
                              }}
                              style={{
                                padding: '0.75rem 1.5rem',
                                background: 'linear-gradient(135deg, #ec4899, #be185d)',
                                color: 'white',
                                border: 'none',
                                borderRadius: '0.5rem',
                                fontWeight: '600',
                                cursor: 'pointer',
                                fontSize: '0.875rem'
                              }}
                            >
                              💾 Download
                            </button>
                            <button
                              onClick={() => setGeneratedImage(null)}
                              style={{
                                padding: '0.75rem 1.5rem',
                                background: '#6b7280',
                                color: 'white',
                                border: 'none',
                                borderRadius: '0.5rem',
                                fontWeight: '600',
                                cursor: 'pointer',
                                fontSize: '0.875rem'
                              }}
                            >
                              🗑️ Clear
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}

              {activeSection === 'preview' && (
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '2rem' }}>
                  <NailArtPreview selectedStyle={selectedStyle} selectedColors={selectedColors} />
                  <StyleOptions
                    onStyleSelect={handleStyleSelect}
                    onColorsSelect={handleColorsSelect}
                    selectedStyle={selectedStyle}
                    selectedColors={selectedColors}
                  />
                </div>
              )}

              {activeSection === 'customization' && (
                <CustomizationPanel
                  type="nail"
                  onCustomize={handleCustomizationChange}
                />
              )}
            </div>
          )}

          {currentStep === 'analyzing' && (
            <div className="text-center" style={{ padding: '4rem 2rem' }}>
              <div style={{
                width: '5rem',
                height: '5rem',
                border: '4px solid rgba(236, 72, 153, 0.3)',
                borderTop: '4px solid #ec4899',
                borderRadius: '50%',
                margin: '0 auto 2rem auto',
                animation: 'spin 1s linear infinite'
              }}></div>
              <h2 style={{ fontSize: '1.5rem', fontWeight: '600', color: '#374151', marginBottom: '1rem' }}>
                Analyzing Your Nails...
              </h2>
              <p style={{ color: '#6b7280', marginBottom: '2rem' }}>
                Our AI is examining your nail shape, length, and current condition to predict the perfect patterns
              </p>
              
              {analysis && (
                <div style={{
                  background: 'rgba(255, 255, 255, 0.8)',
                  borderRadius: '1rem',
                  padding: '1.5rem',
                  maxWidth: '400px',
                  margin: '0 auto',
                  textAlign: 'left'
                }}>
                  <h3 style={{ fontSize: '1.125rem', fontWeight: '600', marginBottom: '1rem', color: '#374151' }}>
                    Analysis Complete ✨
                  </h3>
                  <div style={{ fontSize: '0.875rem', color: '#6b7280', lineHeight: '1.6' }}>
                    <p><strong>Shape:</strong> {analysis.nailShape}</p>
                    <p><strong>Length:</strong> {analysis.nailLength}</p>
                    <p><strong>Condition:</strong> {analysis.nailCondition}</p>
                    <p><strong>Confidence:</strong> {Math.round(analysis.confidence * 100)}%</p>
                  </div>
                </div>
              )}
            </div>
          )}

          {currentStep === 'results' && aiResponse && selectedPattern && (
            <div>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '3rem', alignItems: 'start' }}>
                {/* Left side - Simple nail art display */}
                <div>
                  <ExactHandDisplay
                    userHandImage={handImage || undefined}
                    title={`${selectedPattern.name} - Perfect Match!`}
                  />
                </div>

                {/* Right side - Pattern options and details */}
                <div>
                  {/* Hand Image Upload Section */}
                  <div style={{ marginBottom: '2rem' }}>
                    <HandImageUploader
                      onImageUpload={setHandImage}
                      currentImage={handImage || undefined}
                    />
                  </div>

                  <div style={{ marginBottom: '2rem' }}>
                    <h2 style={{ fontSize: '1.5rem', fontWeight: '600', color: '#374151', marginBottom: '1rem' }}>
                      Recommended Patterns
                    </h2>
                    <div style={{ display: 'grid', gap: '1rem' }}>
                      {aiResponse.predictions.slice(0, 4).map((pattern) => (
                        <div
                          key={pattern.id}
                          onClick={() => handlePatternSelect(pattern)}
                          style={{
                            padding: '1rem',
                            background: selectedPattern?.id === pattern.id ? 'rgba(236, 72, 153, 0.1)' : 'rgba(255, 255, 255, 0.8)',
                            borderRadius: '1rem',
                            border: selectedPattern?.id === pattern.id ? '2px solid #ec4899' : '1px solid rgba(0,0,0,0.1)',
                            cursor: 'pointer',
                            transition: 'all 0.3s ease'
                          }}
                        >
                          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '0.5rem' }}>
                            <h3 style={{ margin: 0, fontSize: '1.125rem', fontWeight: '600', color: '#374151' }}>
                              {pattern.name}
                            </h3>
                            <span style={{
                              background: '#10b981',
                              color: 'white',
                              padding: '0.25rem 0.5rem',
                              borderRadius: '0.25rem',
                              fontSize: '0.75rem',
                              fontWeight: '500'
                            }}>
                              {Math.round(pattern.matchScore * 100)}% match
                            </span>
                          </div>
                          <p style={{ margin: '0 0 0.5rem 0', color: '#6b7280', fontSize: '0.875rem' }}>
                            {pattern.description}
                          </p>
                          <div style={{ display: 'flex', gap: '1rem', fontSize: '0.75rem', color: '#9ca3af' }}>
                            <span>⏱️ {pattern.estimatedTime}</span>
                            <span>📊 {pattern.difficulty}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Instructions */}
                  <div style={{
                    background: 'rgba(255, 255, 255, 0.8)',
                    borderRadius: '1rem',
                    padding: '1.5rem',
                    border: '1px solid rgba(236, 72, 153, 0.2)'
                  }}>
                    <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#374151', marginBottom: '1rem' }}>
                      Step-by-Step Instructions
                    </h3>
                    <ol style={{ margin: 0, paddingLeft: '1.5rem', color: '#6b7280', fontSize: '0.875rem', lineHeight: '1.6' }}>
                      {selectedPattern.instructions.map((instruction, index) => (
                        <li key={index} style={{ marginBottom: '0.5rem' }}>
                          {instruction}
                        </li>
                      ))}
                    </ol>
                    
                    <div style={{ marginTop: '1.5rem', display: 'flex', gap: '1rem' }}>
                      <button
                        onClick={resetSession}
                        className="btn-secondary"
                        style={{ flex: 1 }}
                      >
                        Try Another Photo
                      </button>
                      <button
                        className="btn-primary"
                        style={{ flex: 1 }}
                      >
                        Save This Design
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default NailArt;
