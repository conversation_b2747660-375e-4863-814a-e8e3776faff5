import React, { useState } from 'react';

interface CustomizationPanelProps {
  type: 'nail' | 'hair';
  onCustomize: (settings: any) => void;
}

const CustomizationPanel: React.FC<CustomizationPanelProps> = ({ type, onCustomize }) => {
  const [activeTab, setActiveTab] = useState<'basic' | 'advanced' | 'effects'>('basic');
  const [settings, setSettings] = useState({
    brightness: 50,
    contrast: 50,
    saturation: 50,
    hue: 0,
    opacity: 100,
    blur: 0,
    sharpen: 0,
    warmth: 50,
    highlights: 50,
    shadows: 50
  });

  const handleSettingChange = (key: string, value: number) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);
    onCustomize(newSettings);
  };

  const resetSettings = () => {
    const defaultSettings = {
      brightness: 50,
      contrast: 50,
      saturation: 50,
      hue: 0,
      opacity: 100,
      blur: 0,
      sharpen: 0,
      warmth: 50,
      highlights: 50,
      shadows: 50
    };
    setSettings(defaultSettings);
    onCustomize(defaultSettings);
  };

  const presets = type === 'nail' ? [
    { name: 'Natural', settings: { brightness: 45, contrast: 40, saturation: 30, warmth: 55 } },
    { name: 'Vibrant', settings: { brightness: 60, contrast: 70, saturation: 80, warmth: 50 } },
    { name: 'Elegant', settings: { brightness: 40, contrast: 45, saturation: 35, warmth: 45 } },
    { name: 'Bold', settings: { brightness: 65, contrast: 75, saturation: 90, warmth: 50 } }
  ] : [
    { name: 'Natural', settings: { brightness: 50, contrast: 45, saturation: 40, warmth: 60 } },
    { name: 'Glossy', settings: { brightness: 55, contrast: 60, saturation: 50, warmth: 50 } },
    { name: 'Matte', settings: { brightness: 45, contrast: 40, saturation: 35, warmth: 55 } },
    { name: 'Dramatic', settings: { brightness: 40, contrast: 70, saturation: 60, warmth: 45 } }
  ];

  const applyPreset = (preset: any) => {
    const newSettings = { ...settings, ...preset.settings };
    setSettings(newSettings);
    onCustomize(newSettings);
  };

  const SliderControl = ({ label, value, onChange, min = 0, max = 100, step = 1 }: {
    label: string;
    value: number;
    onChange: (value: number) => void;
    min?: number;
    max?: number;
    step?: number;
  }) => (
    <div style={{ marginBottom: '1.5rem' }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '0.5rem'
      }}>
        <label style={{
          fontSize: '0.875rem',
          fontWeight: '600',
          color: '#374151'
        }}>
          {label}
        </label>
        <span style={{
          fontSize: '0.875rem',
          color: '#6b7280',
          background: 'rgba(236, 72, 153, 0.1)',
          padding: '0.25rem 0.5rem',
          borderRadius: '0.25rem'
        }}>
          {value}
        </span>
      </div>
      <input
        type="range"
        min={min}
        max={max}
        step={step}
        value={value}
        onChange={(e) => onChange(Number(e.target.value))}
        style={{
          width: '100%',
          height: '6px',
          borderRadius: '3px',
          background: `linear-gradient(to right, #ec4899 0%, #ec4899 ${((value - min) / (max - min)) * 100}%, #e5e7eb ${((value - min) / (max - min)) * 100}%, #e5e7eb 100%)`,
          outline: 'none',
          cursor: 'pointer'
        }}
      />
    </div>
  );

  return (
    <div style={{
      background: 'rgba(255, 255, 255, 0.9)',
      borderRadius: '1.5rem',
      padding: '2rem',
      boxShadow: '0 8px 24px rgba(0, 0, 0, 0.1)',
      border: '2px solid rgba(168, 85, 247, 0.2)'
    }}>
      <h3 style={{
        fontSize: '1.5rem',
        fontWeight: '600',
        color: '#374151',
        marginBottom: '1rem',
        fontFamily: 'Playfair Display, serif',
        textAlign: 'center'
      }}>
        🎛️ {type === 'nail' ? 'Nail Art' : 'Hairstyle'} Customization
      </h3>

      {/* Tab Navigation */}
      <div style={{
        display: 'flex',
        marginBottom: '2rem',
        background: 'rgba(168, 85, 247, 0.1)',
        borderRadius: '0.75rem',
        padding: '0.25rem'
      }}>
        {['basic', 'advanced', 'effects'].map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab as any)}
            style={{
              flex: 1,
              padding: '0.75rem',
              background: activeTab === tab ? '#a855f7' : 'transparent',
              color: activeTab === tab ? 'white' : '#6b7280',
              border: 'none',
              borderRadius: '0.5rem',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              textTransform: 'capitalize'
            }}
          >
            {tab}
          </button>
        ))}
      </div>

      {/* Presets */}
      <div style={{ marginBottom: '2rem' }}>
        <h4 style={{
          fontSize: '1rem',
          fontWeight: '600',
          color: '#374151',
          marginBottom: '1rem'
        }}>
          🎨 Quick Presets
        </h4>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(100px, 1fr))',
          gap: '0.75rem'
        }}>
          {presets.map((preset, index) => (
            <button
              key={index}
              onClick={() => applyPreset(preset)}
              style={{
                padding: '0.75rem',
                background: 'rgba(168, 85, 247, 0.1)',
                border: '1px solid rgba(168, 85, 247, 0.2)',
                borderRadius: '0.5rem',
                fontSize: '0.875rem',
                fontWeight: '600',
                color: '#374151',
                cursor: 'pointer',
                transition: 'all 0.3s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = 'rgba(168, 85, 247, 0.2)';
                e.currentTarget.style.transform = 'translateY(-2px)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'rgba(168, 85, 247, 0.1)';
                e.currentTarget.style.transform = 'translateY(0)';
              }}
            >
              {preset.name}
            </button>
          ))}
        </div>
      </div>

      {/* Basic Controls */}
      {activeTab === 'basic' && (
        <div>
          <SliderControl
            label="✨ Brightness"
            value={settings.brightness}
            onChange={(value) => handleSettingChange('brightness', value)}
          />
          <SliderControl
            label="🔆 Contrast"
            value={settings.contrast}
            onChange={(value) => handleSettingChange('contrast', value)}
          />
          <SliderControl
            label="🌈 Saturation"
            value={settings.saturation}
            onChange={(value) => handleSettingChange('saturation', value)}
          />
          <SliderControl
            label="🎨 Hue"
            value={settings.hue}
            onChange={(value) => handleSettingChange('hue', value)}
            min={-180}
            max={180}
          />
        </div>
      )}

      {/* Advanced Controls */}
      {activeTab === 'advanced' && (
        <div>
          <SliderControl
            label="👁️ Opacity"
            value={settings.opacity}
            onChange={(value) => handleSettingChange('opacity', value)}
          />
          <SliderControl
            label="🌡️ Warmth"
            value={settings.warmth}
            onChange={(value) => handleSettingChange('warmth', value)}
          />
          <SliderControl
            label="☀️ Highlights"
            value={settings.highlights}
            onChange={(value) => handleSettingChange('highlights', value)}
          />
          <SliderControl
            label="🌑 Shadows"
            value={settings.shadows}
            onChange={(value) => handleSettingChange('shadows', value)}
          />
        </div>
      )}

      {/* Effects Controls */}
      {activeTab === 'effects' && (
        <div>
          <SliderControl
            label="🌫️ Blur"
            value={settings.blur}
            onChange={(value) => handleSettingChange('blur', value)}
            max={10}
            step={0.1}
          />
          <SliderControl
            label="🔍 Sharpen"
            value={settings.sharpen}
            onChange={(value) => handleSettingChange('sharpen', value)}
            max={10}
            step={0.1}
          />
          
          <div style={{
            background: 'rgba(168, 85, 247, 0.1)',
            borderRadius: '0.75rem',
            padding: '1rem',
            marginTop: '1rem'
          }}>
            <p style={{
              fontSize: '0.875rem',
              color: '#374151',
              margin: '0',
              textAlign: 'center'
            }}>
              💡 <strong>Tip:</strong> Use subtle effects for natural results, or go bold for artistic flair!
            </p>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div style={{
        display: 'flex',
        gap: '1rem',
        justifyContent: 'center',
        marginTop: '2rem',
        flexWrap: 'wrap'
      }}>
        <button
          onClick={resetSettings}
          style={{
            padding: '0.75rem 1.5rem',
            background: 'transparent',
            color: '#6b7280',
            border: '2px solid #e5e7eb',
            borderRadius: '0.5rem',
            fontWeight: '600',
            cursor: 'pointer',
            transition: 'all 0.3s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.borderColor = '#a855f7';
            e.currentTarget.style.color = '#a855f7';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.borderColor = '#e5e7eb';
            e.currentTarget.style.color = '#6b7280';
          }}
        >
          🔄 Reset
        </button>
        
        <button
          style={{
            padding: '0.75rem 1.5rem',
            background: 'linear-gradient(135deg, #a855f7, #8b5cf6)',
            color: 'white',
            border: 'none',
            borderRadius: '0.5rem',
            fontWeight: '600',
            cursor: 'pointer',
            transition: 'all 0.3s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translateY(-2px)';
            e.currentTarget.style.boxShadow = '0 8px 20px rgba(168, 85, 247, 0.3)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = 'none';
          }}
        >
          ✅ Apply Changes
        </button>
      </div>
    </div>
  );
};

export default CustomizationPanel;
