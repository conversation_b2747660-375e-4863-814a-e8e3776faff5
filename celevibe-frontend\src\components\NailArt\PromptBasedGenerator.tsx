import React, { useState } from 'react';

interface PromptBasedGeneratorProps {
  onGenerate: (prompt: string, colors: string[]) => void;
}

const PromptBasedGenerator: React.FC<PromptBasedGeneratorProps> = ({ onGenerate }) => {
  const [prompt, setPrompt] = useState('');
  const [selectedColors, setSelectedColors] = useState<string[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);

  const colorOptions = [
    { name: 'Classic Red', value: '#DC143C' },
    { name: 'Deep Burgundy', value: '#4A1A3D' },
    { name: 'Hot Pink', value: '#FF1493' },
    { name: '<PERSON>', value: '#FF6347' },
    { name: 'Pure White', value: '#FFFFFF' },
    { name: 'Soft Pink', value: '#FFB6C1' },
    { name: 'Gold', value: '#FFD700' },
    { name: 'Silver', value: '#C0C0C0' },
    { name: 'Navy Blue', value: '#000080' },
    { name: 'Emerald', value: '#50C878' },
    { name: 'Purple', value: '#8A2BE2' },
    { name: 'Black', value: '#000000' }
  ];

  const promptSuggestions = [
    'Elegant French manicure with delicate floral accents',
    'Bold geometric patterns with metallic gold lines',
    'Gradient sunset colors blending from orange to pink',
    'Minimalist nude nails with single rhinestone accent',
    'Vintage-inspired polka dots on pastel background',
    'Abstract marble effect with swirling patterns',
    'Glittery ombre from dark to light shades',
    'Tropical palm leaf designs on bright background'
  ];

  const toggleColor = (color: string) => {
    setSelectedColors(prev => 
      prev.includes(color) 
        ? prev.filter(c => c !== color)
        : [...prev, color]
    );
  };

  const handleGenerate = async () => {
    if (!prompt.trim()) {
      alert('Please enter a description for your nail art design');
      return;
    }

    setIsGenerating(true);
    try {
      await onGenerate(prompt, selectedColors);
    } finally {
      setIsGenerating(false);
    }
  };

  const useSuggestion = (suggestion: string) => {
    setPrompt(suggestion);
  };

  return (
    <div style={{
      background: 'rgba(255, 255, 255, 0.9)',
      borderRadius: '1.5rem',
      padding: '2rem',
      boxShadow: '0 8px 24px rgba(0, 0, 0, 0.1)',
      border: '2px solid rgba(34, 197, 94, 0.2)'
    }}>
      <h3 style={{
        fontSize: '1.5rem',
        fontWeight: '600',
        color: '#374151',
        marginBottom: '1rem',
        fontFamily: 'Playfair Display, serif',
        textAlign: 'center'
      }}>
        ✨ Custom Nail Art Generator
      </h3>

      <p style={{
        color: '#6b7280',
        marginBottom: '2rem',
        fontSize: '0.875rem',
        textAlign: 'center'
      }}>
        Describe your dream nail art design and select colors to generate AI-powered nail art
      </p>

      {/* Prompt Input */}
      <div style={{ marginBottom: '2rem' }}>
        <label style={{
          display: 'block',
          fontSize: '1rem',
          fontWeight: '600',
          color: '#374151',
          marginBottom: '0.75rem'
        }}>
          🎨 Describe Your Nail Art Design
        </label>
        <textarea
          value={prompt}
          onChange={(e) => setPrompt(e.target.value)}
          placeholder="e.g., Elegant French manicure with delicate rose gold accents and tiny pearls..."
          style={{
            width: '100%',
            minHeight: '100px',
            padding: '1rem',
            border: '2px solid #e5e7eb',
            borderRadius: '0.75rem',
            fontSize: '0.875rem',
            resize: 'vertical',
            fontFamily: 'inherit',
            outline: 'none',
            transition: 'border-color 0.3s ease'
          }}
          onFocus={(e) => e.target.style.borderColor = '#22c55e'}
          onBlur={(e) => e.target.style.borderColor = '#e5e7eb'}
        />
        <p style={{
          fontSize: '0.75rem',
          color: '#6b7280',
          marginTop: '0.5rem'
        }}>
          Be specific about patterns, textures, and style preferences for best results
        </p>
      </div>

      {/* Prompt Suggestions */}
      <div style={{ marginBottom: '2rem' }}>
        <h4 style={{
          fontSize: '1rem',
          fontWeight: '600',
          color: '#374151',
          marginBottom: '1rem'
        }}>
          💡 Inspiration Ideas
        </h4>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '0.75rem'
        }}>
          {promptSuggestions.map((suggestion, index) => (
            <button
              key={index}
              onClick={() => useSuggestion(suggestion)}
              style={{
                padding: '0.75rem',
                background: 'rgba(34, 197, 94, 0.1)',
                border: '1px solid rgba(34, 197, 94, 0.2)',
                borderRadius: '0.5rem',
                fontSize: '0.875rem',
                color: '#374151',
                cursor: 'pointer',
                textAlign: 'left',
                transition: 'all 0.3s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = 'rgba(34, 197, 94, 0.2)';
                e.currentTarget.style.transform = 'translateY(-2px)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'rgba(34, 197, 94, 0.1)';
                e.currentTarget.style.transform = 'translateY(0)';
              }}
            >
              {suggestion}
            </button>
          ))}
        </div>
      </div>

      {/* Color Selection */}
      <div style={{ marginBottom: '2rem' }}>
        <h4 style={{
          fontSize: '1rem',
          fontWeight: '600',
          color: '#374151',
          marginBottom: '1rem'
        }}>
          🌈 Select Colors (Optional)
        </h4>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))',
          gap: '0.75rem'
        }}>
          {colorOptions.map((color, index) => (
            <div
              key={index}
              onClick={() => toggleColor(color.value)}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                padding: '0.75rem',
                border: selectedColors.includes(color.value) ? '2px solid #22c55e' : '2px solid #e5e7eb',
                borderRadius: '0.5rem',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                background: selectedColors.includes(color.value) ? 'rgba(34, 197, 94, 0.1)' : 'white'
              }}
            >
              <div
                style={{
                  width: '24px',
                  height: '24px',
                  backgroundColor: color.value,
                  borderRadius: '50%',
                  border: '2px solid white',
                  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)'
                }}
              />
              <span style={{
                fontSize: '0.875rem',
                color: '#374151',
                fontWeight: selectedColors.includes(color.value) ? '600' : '400'
              }}>
                {color.name}
              </span>
            </div>
          ))}
        </div>

        {/* Selected Colors Display */}
        {selectedColors.length > 0 && (
          <div style={{
            marginTop: '1rem',
            padding: '1rem',
            background: 'rgba(34, 197, 94, 0.1)',
            borderRadius: '0.75rem'
          }}>
            <p style={{
              fontSize: '0.875rem',
              fontWeight: '600',
              color: '#374151',
              marginBottom: '0.75rem'
            }}>
              Selected Colors ({selectedColors.length}):
            </p>
            <div style={{
              display: 'flex',
              gap: '0.5rem',
              flexWrap: 'wrap'
            }}>
              {selectedColors.map((color, index) => (
                <div
                  key={index}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    background: 'white',
                    padding: '0.25rem 0.75rem',
                    borderRadius: '1rem',
                    fontSize: '0.75rem'
                  }}
                >
                  <div
                    style={{
                      width: '16px',
                      height: '16px',
                      backgroundColor: color,
                      borderRadius: '50%',
                      border: '1px solid #e5e7eb'
                    }}
                  />
                  {color}
                  <button
                    onClick={() => toggleColor(color)}
                    style={{
                      background: 'none',
                      border: 'none',
                      color: '#6b7280',
                      cursor: 'pointer',
                      fontSize: '0.875rem'
                    }}
                  >
                    ×
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Generate Button */}
      <div style={{ textAlign: 'center' }}>
        <button
          onClick={handleGenerate}
          disabled={isGenerating || !prompt.trim()}
          style={{
            padding: '1rem 2rem',
            background: isGenerating || !prompt.trim() 
              ? '#9ca3af' 
              : 'linear-gradient(135deg, #22c55e, #16a34a)',
            color: 'white',
            border: 'none',
            borderRadius: '0.75rem',
            fontSize: '1rem',
            fontWeight: '600',
            cursor: isGenerating || !prompt.trim() ? 'not-allowed' : 'pointer',
            transition: 'all 0.3s ease',
            minWidth: '200px'
          }}
        >
          {isGenerating ? (
            <>
              <span style={{ marginRight: '0.5rem' }}>⏳</span>
              Generating...
            </>
          ) : (
            <>
              <span style={{ marginRight: '0.5rem' }}>✨</span>
              Generate Nail Art
            </>
          )}
        </button>

        <p style={{
          fontSize: '0.75rem',
          color: '#6b7280',
          marginTop: '1rem'
        }}>
          Generation may take 30-60 seconds for best quality results
        </p>
      </div>
    </div>
  );
};

export default PromptBasedGenerator;
