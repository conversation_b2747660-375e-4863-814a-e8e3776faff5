<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Nail Art API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        button {
            background: linear-gradient(135deg, #ec4899, #be185d);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }
        #result {
            margin-top: 20px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            min-height: 100px;
        }
        img {
            max-width: 100%;
            border-radius: 10px;
            margin-top: 10px;
        }
        .loading {
            text-align: center;
            font-size: 18px;
        }
        .error {
            color: #ff6b6b;
            background: rgba(255, 107, 107, 0.1);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid rgba(255, 107, 107, 0.3);
        }
        .success {
            color: #51cf66;
            background: rgba(81, 207, 102, 0.1);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid rgba(81, 207, 102, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Nail Art API Test</h1>
        <p>Test the random nail art generation API</p>
        
        <button onclick="testRandomNailArt()" id="testBtn">Generate Random Nail Art</button>
        <button onclick="clearResult()">Clear</button>
        
        <div id="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:9000';
        
        async function testRandomNailArt() {
            const resultDiv = document.getElementById('result');
            const testBtn = document.getElementById('testBtn');
            
            testBtn.disabled = true;
            testBtn.textContent = 'Generating...';
            
            resultDiv.innerHTML = '<div class="loading">🎨 Generating random nail art...<br>This may take 30-60 seconds</div>';
            
            try {
                // Step 1: Start generation
                console.log('Starting nail art generation...');
                const response = await fetch(`${API_BASE}/api/generate-nail-art`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('Generation started:', data);
                
                if (!data.data || !data.data.task_id) {
                    throw new Error('No task ID received from API');
                }
                
                const taskId = data.data.task_id;
                resultDiv.innerHTML = `<div class="loading">⏳ Task created: ${taskId}<br>Waiting for completion...</div>`;
                
                // Step 2: Poll for completion
                const maxAttempts = 30;
                const intervalMs = 2000;
                
                for (let attempt = 0; attempt < maxAttempts; attempt++) {
                    console.log(`Attempt ${attempt + 1}/${maxAttempts}`);
                    
                    const statusResponse = await fetch(`${API_BASE}/api/task-status/${taskId}`);
                    
                    if (!statusResponse.ok) {
                        throw new Error(`Status check failed: ${statusResponse.status}`);
                    }
                    
                    const statusData = await statusResponse.json();
                    console.log('Status:', statusData);
                    
                    if (statusData.data.status === 'COMPLETED') {
                        const imageUrl = statusData.data.generated[0];
                        resultDiv.innerHTML = `
                            <div class="success">✅ Generation completed!</div>
                            <img src="${imageUrl}" alt="Generated Nail Art" />
                            <p><strong>Generated Prompt:</strong> ${data.generated_prompt || 'Random nail art'}</p>
                        `;
                        break;
                    } else if (statusData.data.status === 'FAILED') {
                        throw new Error('Image generation failed');
                    } else {
                        resultDiv.innerHTML = `<div class="loading">⏳ Status: ${statusData.data.status}<br>Attempt ${attempt + 1}/${maxAttempts}</div>`;
                    }
                    
                    if (attempt < maxAttempts - 1) {
                        await new Promise(resolve => setTimeout(resolve, intervalMs));
                    }
                }
                
            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            } finally {
                testBtn.disabled = false;
                testBtn.textContent = 'Generate Random Nail Art';
            }
        }
        
        function clearResult() {
            document.getElementById('result').innerHTML = '';
        }
    </script>
</body>
</html>
