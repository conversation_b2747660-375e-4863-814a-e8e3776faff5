import React, { useState } from 'react';

interface HairstylePreviewProps {
  selectedStyle?: string;
  selectedColor?: string;
}

const HairstylePreview: React.FC<HairstylePreviewProps> = ({ selectedStyle, selectedColor }) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Sample hairstyle images for different styles
  const hairstyleImages = {
    'bob': [
      'https://images.unsplash.com/photo-1560264280-88b68371db39?w=300&h=400&fit=crop&crop=face',
      'https://images.unsplash.com/photo-1594824804732-5f8d5d2e8b8d?w=300&h=400&fit=crop&crop=face'
    ],
    'pixie': [
      'https://images.unsplash.com/photo-1580618672591-eb180b1a973f?w=300&h=400&fit=crop&crop=face',
      'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=300&h=400&fit=crop&crop=face'
    ],
    'long-waves': [
      'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=300&h=400&fit=crop&crop=face',
      'https://images.unsplash.com/photo-1580618672591-eb180b1a973f?w=300&h=400&fit=crop&crop=face'
    ],
    'updo': [
      'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=300&h=400&fit=crop&crop=face',
      'https://images.unsplash.com/photo-1560264280-88b68371db39?w=300&h=400&fit=crop&crop=face'
    ]
  };

  const currentImages = hairstyleImages[selectedStyle as keyof typeof hairstyleImages] || hairstyleImages['bob'];

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % currentImages.length);
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + currentImages.length) % currentImages.length);
  };

  return (
    <div style={{
      background: 'rgba(255, 255, 255, 0.9)',
      borderRadius: '1.5rem',
      padding: '2rem',
      textAlign: 'center',
      boxShadow: '0 8px 24px rgba(0, 0, 0, 0.1)',
      border: '2px solid rgba(139, 92, 246, 0.2)'
    }}>
      <h3 style={{
        fontSize: '1.5rem',
        fontWeight: '600',
        color: '#374151',
        marginBottom: '1rem',
        fontFamily: 'Playfair Display, serif'
      }}>
        💇‍♀️ Hairstyle Preview
      </h3>

      <p style={{
        color: '#6b7280',
        marginBottom: '1.5rem',
        fontSize: '0.875rem'
      }}>
        {selectedStyle ? `Previewing: ${selectedStyle.replace('-', ' ').toUpperCase()}` : 'Select a hairstyle to preview'}
      </p>

      {/* Image Preview */}
      <div style={{
        position: 'relative',
        marginBottom: '1.5rem',
        display: 'inline-block'
      }}>
        <img
          src={currentImages[currentImageIndex]}
          alt={`${selectedStyle || 'hairstyle'} preview`}
          style={{
            width: '250px',
            height: '300px',
            objectFit: 'cover',
            borderRadius: '1rem',
            boxShadow: '0 8px 20px rgba(0, 0, 0, 0.15)'
          }}
        />

        {/* Navigation arrows */}
        {currentImages.length > 1 && (
          <>
            <button
              onClick={prevImage}
              style={{
                position: 'absolute',
                left: '10px',
                top: '50%',
                transform: 'translateY(-50%)',
                background: 'rgba(0, 0, 0, 0.6)',
                color: 'white',
                border: 'none',
                borderRadius: '50%',
                width: '35px',
                height: '35px',
                cursor: 'pointer',
                fontSize: '1.2rem',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              ‹
            </button>
            <button
              onClick={nextImage}
              style={{
                position: 'absolute',
                right: '10px',
                top: '50%',
                transform: 'translateY(-50%)',
                background: 'rgba(0, 0, 0, 0.6)',
                color: 'white',
                border: 'none',
                borderRadius: '50%',
                width: '35px',
                height: '35px',
                cursor: 'pointer',
                fontSize: '1.2rem',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              ›
            </button>
          </>
        )}

        {/* Image counter */}
        <div style={{
          position: 'absolute',
          bottom: '10px',
          left: '50%',
          transform: 'translateX(-50%)',
          background: 'rgba(0, 0, 0, 0.7)',
          color: 'white',
          padding: '0.25rem 0.75rem',
          borderRadius: '1rem',
          fontSize: '0.75rem'
        }}>
          {currentImageIndex + 1} / {currentImages.length}
        </div>
      </div>

      {/* Color indicator */}
      {selectedColor && (
        <div style={{ marginBottom: '1rem' }}>
          <p style={{ fontSize: '0.875rem', color: '#374151', marginBottom: '0.5rem' }}>
            Selected Color:
          </p>
          <div style={{
            display: 'flex',
            gap: '0.5rem',
            justifyContent: 'center',
            alignItems: 'center'
          }}>
            <div
              style={{
                width: '30px',
                height: '30px',
                backgroundColor: selectedColor,
                borderRadius: '50%',
                border: '2px solid white',
                boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)'
              }}
              title={selectedColor}
            />
            <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>
              {selectedColor}
            </span>
          </div>
        </div>
      )}

      {/* Style info */}
      <div style={{
        background: 'rgba(139, 92, 246, 0.1)',
        borderRadius: '0.75rem',
        padding: '1rem',
        fontSize: '0.875rem',
        color: '#374151'
      }}>
        <p style={{ margin: '0', fontWeight: '600', marginBottom: '0.5rem' }}>
          ✨ Style Details
        </p>
        <p style={{ margin: '0' }}>
          {selectedStyle === 'bob' && 'Classic bob cut - timeless and versatile for any occasion'}
          {selectedStyle === 'pixie' && 'Short and chic pixie cut - bold and modern style'}
          {selectedStyle === 'long-waves' && 'Long flowing waves - romantic and elegant look'}
          {selectedStyle === 'updo' && 'Elegant updo - perfect for formal events and special occasions'}
          {!selectedStyle && 'Choose a hairstyle from the options to see detailed information'}
        </p>
      </div>
    </div>
  );
};

export default HairstylePreview;
