import React, { useState } from 'react';

interface NailArtPreviewProps {
  selectedStyle?: string;
  selectedColors?: string[];
}

const NailArtPreview: React.FC<NailArtPreviewProps> = ({ selectedStyle, selectedColors }) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Sample nail art images for different styles
  const nailArtStyles = {
    'french-tips': [
      'https://images.unsplash.com/photo-1610992015732-2449b76344bc?w=300&h=300&fit=crop',
      'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=300&h=300&fit=crop'
    ],
    'burgundy': [
      'https://images.unsplash.com/photo-1604654894610-df63bc536371?w=300&h=300&fit=crop',
      'https://images.unsplash.com/photo-1583847268964-b28dc8f51f92?w=300&h=300&fit=crop'
    ],
    'gradient': [
      'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=300&h=300&fit=crop',
      'https://images.unsplash.com/photo-1607779097040-26e80aa78e66?w=300&h=300&fit=crop'
    ],
    'floral': [
      'https://images.unsplash.com/photo-1583847268964-b28dc8f51f92?w=300&h=300&fit=crop',
      'https://images.unsplash.com/photo-1604654894610-df63bc536371?w=300&h=300&fit=crop'
    ]
  };

  const currentImages = nailArtStyles[selectedStyle as keyof typeof nailArtStyles] || nailArtStyles['french-tips'];

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % currentImages.length);
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + currentImages.length) % currentImages.length);
  };

  return (
    <div style={{
      background: 'rgba(255, 255, 255, 0.9)',
      borderRadius: '1.5rem',
      padding: '2rem',
      textAlign: 'center',
      boxShadow: '0 8px 24px rgba(0, 0, 0, 0.1)',
      border: '2px solid rgba(236, 72, 153, 0.2)'
    }}>
      <h3 style={{
        fontSize: '1.5rem',
        fontWeight: '600',
        color: '#374151',
        marginBottom: '1rem',
        fontFamily: 'Playfair Display, serif'
      }}>
        💅 Nail Art Preview
      </h3>

      <p style={{
        color: '#6b7280',
        marginBottom: '1.5rem',
        fontSize: '0.875rem'
      }}>
        {selectedStyle ? `Previewing: ${selectedStyle.replace('-', ' ').toUpperCase()}` : 'Select a style to preview'}
      </p>

      {/* Image Preview */}
      <div style={{
        position: 'relative',
        marginBottom: '1.5rem',
        display: 'inline-block'
      }}>
        <img
          src={currentImages[currentImageIndex]}
          alt={`${selectedStyle || 'nail art'} preview`}
          style={{
            width: '250px',
            height: '250px',
            objectFit: 'cover',
            borderRadius: '1rem',
            boxShadow: '0 8px 20px rgba(0, 0, 0, 0.15)'
          }}
        />

        {/* Navigation arrows */}
        {currentImages.length > 1 && (
          <>
            <button
              onClick={prevImage}
              style={{
                position: 'absolute',
                left: '10px',
                top: '50%',
                transform: 'translateY(-50%)',
                background: 'rgba(0, 0, 0, 0.6)',
                color: 'white',
                border: 'none',
                borderRadius: '50%',
                width: '35px',
                height: '35px',
                cursor: 'pointer',
                fontSize: '1.2rem',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              ‹
            </button>
            <button
              onClick={nextImage}
              style={{
                position: 'absolute',
                right: '10px',
                top: '50%',
                transform: 'translateY(-50%)',
                background: 'rgba(0, 0, 0, 0.6)',
                color: 'white',
                border: 'none',
                borderRadius: '50%',
                width: '35px',
                height: '35px',
                cursor: 'pointer',
                fontSize: '1.2rem',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              ›
            </button>
          </>
        )}

        {/* Image counter */}
        <div style={{
          position: 'absolute',
          bottom: '10px',
          left: '50%',
          transform: 'translateX(-50%)',
          background: 'rgba(0, 0, 0, 0.7)',
          color: 'white',
          padding: '0.25rem 0.75rem',
          borderRadius: '1rem',
          fontSize: '0.75rem'
        }}>
          {currentImageIndex + 1} / {currentImages.length}
        </div>
      </div>

      {/* Color indicators */}
      {selectedColors && selectedColors.length > 0 && (
        <div style={{ marginBottom: '1rem' }}>
          <p style={{ fontSize: '0.875rem', color: '#374151', marginBottom: '0.5rem' }}>
            Selected Colors:
          </p>
          <div style={{
            display: 'flex',
            gap: '0.5rem',
            justifyContent: 'center',
            flexWrap: 'wrap'
          }}>
            {selectedColors.map((color, index) => (
              <div
                key={index}
                style={{
                  width: '30px',
                  height: '30px',
                  backgroundColor: color,
                  borderRadius: '50%',
                  border: '2px solid white',
                  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)'
                }}
                title={color}
              />
            ))}
          </div>
        </div>
      )}

      {/* Style info */}
      <div style={{
        background: 'rgba(236, 72, 153, 0.1)',
        borderRadius: '0.75rem',
        padding: '1rem',
        fontSize: '0.875rem',
        color: '#374151'
      }}>
        <p style={{ margin: '0', fontWeight: '600', marginBottom: '0.5rem' }}>
          ✨ Style Details
        </p>
        <p style={{ margin: '0' }}>
          {selectedStyle === 'french-tips' && 'Classic French manicure with white tips and nude base'}
          {selectedStyle === 'burgundy' && 'Deep burgundy color with glossy finish - perfect for elegance'}
          {selectedStyle === 'gradient' && 'Smooth color transitions creating beautiful ombre effects'}
          {selectedStyle === 'floral' && 'Delicate floral patterns with artistic details'}
          {!selectedStyle && 'Choose a style from the options to see detailed information'}
        </p>
      </div>
    </div>
  );
};

export default NailArtPreview;
