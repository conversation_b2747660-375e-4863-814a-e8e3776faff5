const express = require('express');
const cors = require('cors');
const axios = require('axios');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3005;

// Freepik API configuration
const FREEPIK_API_KEY = 'FPSX7f1836a11ecbc6965b7c81275c729e55';
const FREEPIK_BASE_URL = 'https://api.freepik.com/v1/ai/mystic';

// Middleware
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:3000'],
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'CeleVibe Backend is running' });
});

// Random nail art patterns
const randomNailPatterns = [
  "Beautiful floral nail art with roses and leaves",
  "Geometric nail design with triangles and lines",
  "Galaxy nail art with stars and nebula",
  "Marble effect nail design with gold veins",
  "French manicure with glitter tips",
  "Ombre nail design from pink to purple",
  "Polka dot nail art in pastel colors",
  "Abstract nail art with swirls and curves",
  "Butterfly nail design with colorful wings",
  "Minimalist nail art with thin lines",
  "Tropical nail design with palm leaves",
  "Vintage lace pattern nail art",
  "Sunset gradient nail design",
  "Animal print nail art - leopard pattern",
  "Holographic nail design with rainbow effect",
  "Cherry blossom nail art with pink flowers",
  "Mandala pattern nail design",
  "Ocean wave nail art in blue tones",
  "Glitter gradient nail design",
  "Art deco inspired nail pattern",
  "Watercolor effect nail art",
  "Striped nail design in multiple colors",
  "Heart pattern nail art for romance",
  "Tribal pattern nail design",
  "Jewel-toned nail art with gems effect"
];

const randomColors = [
  "pink", "purple", "blue", "red", "gold", "silver", "black", "white",
  "coral", "mint", "lavender", "rose gold", "emerald", "navy", "burgundy",
  "turquoise", "magenta", "champagne", "bronze", "pearl", "nude", "cream"
];

// Generate random nail art image
app.post('/api/generate-nail-art', async (req, res) => {
  try {
    // Generate random nail pattern
    const randomPattern = randomNailPatterns[Math.floor(Math.random() * randomNailPatterns.length)];
    const randomColor1 = randomColors[Math.floor(Math.random() * randomColors.length)];
    const randomColor2 = randomColors[Math.floor(Math.random() * randomColors.length)];

    const randomPrompt = `${randomPattern} in ${randomColor1} and ${randomColor2} colors, professional nail art, high quality, detailed, beautiful hands`;

    console.log('Generating random nail art with prompt:', randomPrompt);

    const requestBody = {
      prompt: randomPrompt,
      negative_prompt: "blurry, low quality, distorted, ugly, bad anatomy",
      num_images: 1,
      image: {
        size: "square_1_1"
      }
    };

    const response = await axios.post(FREEPIK_BASE_URL, requestBody, {
      headers: {
        'Content-Type': 'application/json',
        'x-freepik-api-key': FREEPIK_API_KEY
      }
    });

    console.log('Freepik API response status:', response.status);
    console.log('Freepik API success:', response.data);
    res.json({
      ...response.data,
      generated_prompt: randomPrompt
    });

  } catch (error) {
    console.error('Server error:', error);
    if (error.response) {
      // Axios error with response
      res.status(error.response.status).json({
        error: 'Freepik API error',
        details: error.response.data,
        status: error.response.status
      });
    } else {
      res.status(500).json({
        error: 'Internal server error',
        message: error.message
      });
    }
  }
});

// Generate random nail art (GET endpoint for easy testing)
app.get('/api/generate-random-nail-art', async (req, res) => {
  try {
    // Generate random nail pattern
    const randomPattern = randomNailPatterns[Math.floor(Math.random() * randomNailPatterns.length)];
    const randomColor1 = randomColors[Math.floor(Math.random() * randomColors.length)];
    const randomColor2 = randomColors[Math.floor(Math.random() * randomColors.length)];

    const randomPrompt = `${randomPattern} in ${randomColor1} and ${randomColor2} colors, professional nail art, high quality, detailed, beautiful hands`;

    console.log('Generating random nail art with prompt:', randomPrompt);

    const requestBody = {
      prompt: randomPrompt,
      negative_prompt: "blurry, low quality, distorted, ugly, bad anatomy",
      num_images: 1,
      image: {
        size: "square_1_1"
      }
    };

    const response = await axios.post(FREEPIK_BASE_URL, requestBody, {
      headers: {
        'Content-Type': 'application/json',
        'x-freepik-api-key': FREEPIK_API_KEY
      }
    });

    console.log('Freepik API response status:', response.status);
    console.log('Freepik API success:', response.data);
    res.json({
      ...response.data,
      generated_prompt: randomPrompt
    });

  } catch (error) {
    console.error('Server error:', error);
    if (error.response) {
      // Axios error with response
      res.status(error.response.status).json({
        error: 'Freepik API error',
        details: error.response.data,
        status: error.response.status
      });
    } else {
      res.status(500).json({
        error: 'Internal server error',
        message: error.message
      });
    }
  }
});

// Check task status
app.get('/api/task-status/:taskId', async (req, res) => {
  try {
    const { taskId } = req.params;
    console.log('Checking task status for:', taskId);

    const response = await axios.get(`${FREEPIK_BASE_URL}/${taskId}`, {
      headers: {
        'x-freepik-api-key': FREEPIK_API_KEY
      }
    });

    console.log('Task status:', response.data);
    res.json(response.data);

  } catch (error) {
    console.error('Task status error:', error);
    if (error.response) {
      res.status(error.response.status).json({
        error: 'Task status check failed',
        details: error.response.data
      });
    } else {
      res.status(500).json({
        error: 'Internal server error',
        message: error.message
      });
    }
  }
});

// Generate burgundy nail art (preset)
app.post('/api/generate-burgundy-nails', async (req, res) => {
  const requestBody = {
    prompt: "Professional photograph of elegant female hands with dark burgundy nail polish, glossy finish, perfect manicure, oval shaped nails, light skin tone, fingers slightly spread, clean white background, studio lighting, photorealistic, ultra-detailed, beauty photography, 8k resolution, luxury nail art",
    resolution: "2k",
    aspect_ratio: "classic_4_3",
    model: "realism",
    creative_detailing: 50,
    engine: "magnific_sharpy",
    fixed_generation: false,
    filter_nsfw: true,
    styling: {
      colors: [
        {
          color: "#4A1A3D",
          weight: 0.8
        }
      ]
    }
  };

  try {
    console.log('Generating burgundy nails with preset...');

    const response = await axios.post(FREEPIK_BASE_URL, requestBody, {
      headers: {
        'Content-Type': 'application/json',
        'x-freepik-api-key': FREEPIK_API_KEY
      }
    });

    console.log('Burgundy nails generation started:', response.data);
    res.json(response.data);

  } catch (error) {
    console.error('Burgundy nails error:', error);
    if (error.response) {
      res.status(error.response.status).json({
        error: 'Generation failed',
        details: error.response.data
      });
    } else {
      res.status(500).json({
        error: 'Internal server error',
        message: error.message
      });
    }
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 CeleVibe Backend running on port ${PORT}`);
  console.log(`📡 Health check: http://localhost:${PORT}/health`);
  console.log(`🎨 Random Nail Art API (POST): http://localhost:${PORT}/api/generate-nail-art`);
  console.log(`🎨 Random Nail Art API (GET): http://localhost:${PORT}/api/generate-random-nail-art`);
  console.log(`🔍 Task Status API: http://localhost:${PORT}/api/task-status/:taskId`);
});

module.exports = app;
