const express = require('express');
const cors = require('cors');
const axios = require('axios');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3004;

// Freepik API configuration
const FREEPIK_API_KEY = 'FPSX7f1836a11ecbc6965b7c81275c729e55';
const FREEPIK_BASE_URL = 'https://api.freepik.com/v1/ai/mystic';

// Middleware
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:3000'],
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'CeleVibe Backend is running' });
});

// Generate nail art image
app.post('/api/generate-nail-art', async (req, res) => {
  try {
    console.log('Received nail art generation request:', req.body);

    const response = await axios.post(FREEPIK_BASE_URL, req.body, {
      headers: {
        'Content-Type': 'application/json',
        'x-freepik-api-key': FREEPIK_API_KEY
      }
    });

    console.log('Freepik API response status:', response.status);
    console.log('Freepik API success:', response.data);
    res.json(response.data);

  } catch (error) {
    console.error('Server error:', error);
    if (error.response) {
      // Axios error with response
      res.status(error.response.status).json({
        error: 'Freepik API error',
        details: error.response.data,
        status: error.response.status
      });
    } else {
      res.status(500).json({
        error: 'Internal server error',
        message: error.message
      });
    }
  }
});

// Check task status
app.get('/api/task-status/:taskId', async (req, res) => {
  try {
    const { taskId } = req.params;
    console.log('Checking task status for:', taskId);

    const response = await axios.get(`${FREEPIK_BASE_URL}/${taskId}`, {
      headers: {
        'x-freepik-api-key': FREEPIK_API_KEY
      }
    });

    console.log('Task status:', response.data);
    res.json(response.data);

  } catch (error) {
    console.error('Task status error:', error);
    if (error.response) {
      res.status(error.response.status).json({
        error: 'Task status check failed',
        details: error.response.data
      });
    } else {
      res.status(500).json({
        error: 'Internal server error',
        message: error.message
      });
    }
  }
});

// Generate burgundy nail art (preset)
app.post('/api/generate-burgundy-nails', async (req, res) => {
  const requestBody = {
    prompt: "Professional photograph of elegant female hands with dark burgundy nail polish, glossy finish, perfect manicure, oval shaped nails, light skin tone, fingers slightly spread, clean white background, studio lighting, photorealistic, ultra-detailed, beauty photography, 8k resolution, luxury nail art",
    resolution: "2k",
    aspect_ratio: "classic_4_3",
    model: "realism",
    creative_detailing: 50,
    engine: "magnific_sharpy",
    fixed_generation: false,
    filter_nsfw: true,
    styling: {
      colors: [
        {
          color: "#4A1A3D",
          weight: 0.8
        }
      ]
    }
  };

  try {
    console.log('Generating burgundy nails with preset...');

    const response = await axios.post(FREEPIK_BASE_URL, requestBody, {
      headers: {
        'Content-Type': 'application/json',
        'x-freepik-api-key': FREEPIK_API_KEY
      }
    });

    console.log('Burgundy nails generation started:', response.data);
    res.json(response.data);

  } catch (error) {
    console.error('Burgundy nails error:', error);
    if (error.response) {
      res.status(error.response.status).json({
        error: 'Generation failed',
        details: error.response.data
      });
    } else {
      res.status(500).json({
        error: 'Internal server error',
        message: error.message
      });
    }
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 CeleVibe Backend running on port ${PORT}`);
  console.log(`📡 Health check: http://localhost:${PORT}/health`);
  console.log(`🎨 Nail art API: http://localhost:${PORT}/api/generate-nail-art`);
});

module.exports = app;
