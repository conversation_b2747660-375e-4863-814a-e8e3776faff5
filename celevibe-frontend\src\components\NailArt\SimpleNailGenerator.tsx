import React, { useState } from 'react';

const SimpleNailGenerator: React.FC = () => {
  const [generatedImages, setGeneratedImages] = useState<string[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // For now, let's use placeholder images that look like AI-generated nail art
  // In production, you would integrate with the Freepik API through a backend
  const generateNailArt = async (style: string) => {
    setIsGenerating(true);
    setError(null);
    setGeneratedImages([]);

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      // For demonstration, we'll use high-quality nail art images
      // In production, these would come from the Freepik API
      const sampleImages = {
        burgundy: [
          'https://images.unsplash.com/photo-1604654894610-df63bc536371?w=400&h=300&fit=crop&crop=hands',
          'https://images.unsplash.com/photo-1583847268964-b28dc8f51f92?w=400&h=300&fit=crop&crop=hands',
          'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400&h=300&fit=crop&crop=hands'
        ],
        french: [
          'https://images.unsplash.com/photo-1610992015732-2449b76344bc?w=400&h=300&fit=crop&crop=hands',
          'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=400&h=300&fit=crop&crop=hands',
          'https://images.unsplash.com/photo-1607779097040-26e80aa78e66?w=400&h=300&fit=crop&crop=hands'
        ],
        gradient: [
          'https://images.unsplash.com/photo-1604654894610-df63bc536371?w=400&h=300&fit=crop&crop=hands',
          'https://images.unsplash.com/photo-1583847268964-b28dc8f51f92?w=400&h=300&fit=crop&crop=hands'
        ]
      };

      const images = sampleImages[style as keyof typeof sampleImages] || sampleImages.burgundy;
      setGeneratedImages(images);

    } catch (err) {
      setError('Failed to generate nail art. Please try again.');
      console.error('Generation error:', err);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div style={{
      background: 'rgba(255, 255, 255, 0.9)',
      borderRadius: '1.5rem',
      padding: '2rem',
      margin: '2rem 0',
      textAlign: 'center',
      boxShadow: '0 8px 24px rgba(0, 0, 0, 0.1)'
    }}>
      <h3 style={{
        fontSize: '1.5rem',
        fontWeight: '600',
        color: '#374151',
        marginBottom: '1.5rem',
        fontFamily: 'Playfair Display, serif'
      }}>
        🎨 AI Nail Art Generator
      </h3>

      <p style={{
        color: '#6b7280',
        marginBottom: '2rem',
        fontSize: '0.875rem'
      }}>
        Generate beautiful nail art designs using AI. Choose a style below to get started.
      </p>

      {/* Style Selection Buttons */}
      <div style={{
        display: 'flex',
        gap: '1rem',
        justifyContent: 'center',
        marginBottom: '2rem',
        flexWrap: 'wrap'
      }}>
        <button
          onClick={() => generateNailArt('burgundy')}
          disabled={isGenerating}
          style={{
            padding: '1rem 1.5rem',
            background: isGenerating ? '#9ca3af' : 'linear-gradient(135deg, #4A1A3D, #6B2C5A)',
            color: 'white',
            border: 'none',
            borderRadius: '0.75rem',
            fontWeight: '600',
            cursor: isGenerating ? 'not-allowed' : 'pointer',
            fontSize: '0.875rem',
            transition: 'all 0.3s ease'
          }}
        >
          💅 Dark Burgundy
        </button>

        <button
          onClick={() => generateNailArt('french')}
          disabled={isGenerating}
          style={{
            padding: '1rem 1.5rem',
            background: isGenerating ? '#9ca3af' : 'linear-gradient(135deg, #ec4899, #be185d)',
            color: 'white',
            border: 'none',
            borderRadius: '0.75rem',
            fontWeight: '600',
            cursor: isGenerating ? 'not-allowed' : 'pointer',
            fontSize: '0.875rem',
            transition: 'all 0.3s ease'
          }}
        >
          ✨ French Tips
        </button>

        <button
          onClick={() => generateNailArt('gradient')}
          disabled={isGenerating}
          style={{
            padding: '1rem 1.5rem',
            background: isGenerating ? '#9ca3af' : 'linear-gradient(135deg, #6366f1, #8b5cf6)',
            color: 'white',
            border: 'none',
            borderRadius: '0.75rem',
            fontWeight: '600',
            cursor: isGenerating ? 'not-allowed' : 'pointer',
            fontSize: '0.875rem',
            transition: 'all 0.3s ease'
          }}
        >
          🌈 Gradient
        </button>
      </div>

      {/* Generation Status */}
      <div style={{ minHeight: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        {isGenerating ? (
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: '1rem'
          }}>
            <div style={{
              width: '60px',
              height: '60px',
              border: '4px solid #f3f4f6',
              borderTop: '4px solid #ec4899',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite'
            }} />
            <div>
              <p style={{ color: '#ec4899', fontWeight: '600', marginBottom: '0.5rem' }}>
                ✨ Creating your perfect nail art...
              </p>
              <p style={{ color: '#6b7280', fontSize: '0.875rem' }}>
                Generating high-quality images
              </p>
            </div>
          </div>
        ) : error ? (
          <div style={{
            padding: '2rem',
            background: 'rgba(239, 68, 68, 0.1)',
            borderRadius: '1rem',
            border: '1px solid rgba(239, 68, 68, 0.2)',
            maxWidth: '400px'
          }}>
            <p style={{ color: '#dc2626', marginBottom: '1rem' }}>❌ {error}</p>
            <p style={{ color: '#6b7280', fontSize: '0.875rem' }}>
              Please try selecting a different style.
            </p>
          </div>
        ) : generatedImages.length > 0 ? (
          <div style={{ width: '100%' }}>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
              gap: '1rem',
              marginBottom: '1rem'
            }}>
              {generatedImages.map((image, index) => (
                <div key={index} style={{ position: 'relative' }}>
                  <img
                    src={image}
                    alt={`Generated nail art ${index + 1}`}
                    style={{
                      width: '100%',
                      height: '200px',
                      objectFit: 'cover',
                      borderRadius: '1rem',
                      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
                    }}
                  />
                  <div style={{
                    position: 'absolute',
                    top: '0.5rem',
                    right: '0.5rem',
                    background: 'rgba(0, 0, 0, 0.7)',
                    color: 'white',
                    padding: '0.25rem 0.5rem',
                    borderRadius: '0.25rem',
                    fontSize: '0.75rem'
                  }}>
                    {index + 1}
                  </div>
                </div>
              ))}
            </div>
            
            <p style={{
              color: '#10b981',
              fontWeight: '600',
              marginBottom: '1rem'
            }}>
              ✅ Generated {generatedImages.length} nail art design{generatedImages.length > 1 ? 's' : ''}!
            </p>

            <div style={{
              display: 'flex',
              gap: '1rem',
              justifyContent: 'center',
              flexWrap: 'wrap'
            }}>
              <button
                style={{
                  padding: '0.75rem 1.5rem',
                  background: 'linear-gradient(135deg, #ec4899, #be185d)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '0.5rem',
                  fontWeight: '600',
                  cursor: 'pointer'
                }}
              >
                💾 Save Favorites
              </button>
              
              <button
                style={{
                  padding: '0.75rem 1.5rem',
                  background: 'transparent',
                  color: '#ec4899',
                  border: '2px solid #ec4899',
                  borderRadius: '0.5rem',
                  fontWeight: '600',
                  cursor: 'pointer'
                }}
              >
                📱 Book Appointment
              </button>
            </div>
          </div>
        ) : (
          <div style={{
            padding: '2rem',
            background: 'rgba(59, 130, 246, 0.1)',
            borderRadius: '1rem',
            border: '1px solid rgba(59, 130, 246, 0.2)'
          }}>
            <p style={{ color: '#2563eb', fontSize: '1.125rem', marginBottom: '0.5rem' }}>
              🎨 Ready to create beautiful nail art!
            </p>
            <p style={{ color: '#6b7280', fontSize: '0.875rem' }}>
              Choose a style above to generate AI-powered nail art designs
            </p>
          </div>
        )}
      </div>

      {/* Info Section */}
      <div style={{
        marginTop: '2rem',
        padding: '1rem',
        background: 'rgba(236, 72, 153, 0.1)',
        borderRadius: '1rem',
        fontSize: '0.875rem',
        color: '#374151'
      }}>
        <p style={{ margin: '0', fontWeight: '600', marginBottom: '0.5rem' }}>
          🚀 Coming Soon: Full Freepik API Integration
        </p>
        <p style={{ margin: '0' }}>
          We're working on integrating the full Freepik Mystic AI API to generate custom nail art designs. 
          For now, enjoy these curated high-quality nail art examples!
        </p>
      </div>
    </div>
  );
};

export default SimpleNailGenerator;
