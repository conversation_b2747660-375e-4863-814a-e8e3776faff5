import React, { useState } from 'react';
import NailArt from './pages/NailArt/NailArt';
import Pricing from './pages/Pricing/Pricing';
import { generateCustomNailArt, waitForTaskCompletion } from './services/freepikAPI';

function App() {
  const [currentPage, setCurrentPage] = useState('home');

  const renderPage = () => {
    switch (currentPage) {
      case 'demo':
        return <DemoPage />;
      case 'nail-art':
        return <NailArt />;
      case 'hairstyles':
        return <HairstylesPage />;
      case 'pricing':
        return <Pricing />;
      default:
        return <HomePage />;
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header currentPage={currentPage} setCurrentPage={setCurrentPage} />
      <main className="flex-grow">
        {renderPage()}
      </main>
      <Footer />
    </div>
  );
}

// Simple Header Component
const Header = ({ currentPage, setCurrentPage }: { currentPage: string; setCurrentPage: (page: string) => void }) => {
  return (
    <header className="fixed w-full top-0 z-50" style={{ background: 'rgba(255, 255, 255, 0.8)', backdropFilter: 'blur(16px)', borderBottom: '1px solid rgba(255, 255, 255, 0.2)' }}>
      <nav className="container">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center" style={{ gap: '0.5rem' }}>
            <div style={{
              width: '2rem',
              height: '2rem',
              background: 'linear-gradient(135deg, #ec4899 0%, #a855f7 100%)',
              borderRadius: '0.5rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <span style={{ color: 'white', fontWeight: 'bold', fontSize: '1.125rem' }}>C</span>
            </div>
            <span className="gradient-text" style={{ fontSize: '1.25rem', fontFamily: 'Playfair Display, serif', fontWeight: 'bold' }}>
              CeleVibe
            </span>
          </div>

          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
            <button
              onClick={() => setCurrentPage('home')}
              style={{
                padding: '0.5rem 1rem',
                borderRadius: '0.5rem',
                fontSize: '0.875rem',
                fontWeight: '500',
                border: 'none',
                background: currentPage === 'home' ? 'rgba(236, 72, 153, 0.1)' : 'transparent',
                color: currentPage === 'home' ? '#ec4899' : '#374151',
                cursor: 'pointer'
              }}
            >
              Home
            </button>
            <button
              onClick={() => setCurrentPage('nail-art')}
              style={{
                padding: '0.5rem 1rem',
                borderRadius: '0.5rem',
                fontSize: '0.875rem',
                fontWeight: '500',
                border: 'none',
                background: currentPage === 'nail-art' ? 'rgba(236, 72, 153, 0.1)' : 'transparent',
                color: currentPage === 'nail-art' ? '#ec4899' : '#374151',
                cursor: 'pointer'
              }}
            >
              AI Nail Art
            </button>
            <button
              onClick={() => setCurrentPage('pricing')}
              style={{
                padding: '0.5rem 1rem',
                borderRadius: '0.5rem',
                fontSize: '0.875rem',
                fontWeight: '500',
                border: 'none',
                background: currentPage === 'pricing' ? 'rgba(236, 72, 153, 0.1)' : 'transparent',
                color: currentPage === 'pricing' ? '#ec4899' : '#374151',
                cursor: 'pointer'
              }}
            >
              Pricing
            </button>
            <button
              onClick={() => setCurrentPage('demo')}
              className="btn-primary"
            >
              Try Demo
            </button>
          </div>
        </div>
      </nav>
    </header>
  );
};

// Simple Home Page
const HomePage = () => {
  return (
    <div className="pt-16">
      <section className="relative min-h-screen flex items-center justify-center">
        <div className="container text-center">
          <div style={{ marginBottom: '2rem' }}>
            <div style={{
              display: 'inline-flex',
              alignItems: 'center',
              padding: '0.5rem 1rem',
              borderRadius: '9999px',
              background: 'rgba(255, 255, 255, 0.2)',
              backdropFilter: 'blur(16px)',
              border: '1px solid rgba(255, 255, 255, 0.3)',
              color: '#ec4899',
              fontSize: '0.875rem',
              fontWeight: '500',
              marginBottom: '2rem'
            }}>
              <span style={{ width: '0.5rem', height: '0.5rem', background: '#ec4899', borderRadius: '50%', marginRight: '0.5rem' }}></span>
              AI-Powered Beauty Generation
            </div>
          </div>

          <h1 style={{ fontSize: '3rem', fontFamily: 'Playfair Display, serif', fontWeight: 'bold', marginBottom: '1.5rem', lineHeight: '1.1' }}>
            <span className="gradient-text">Transform Your</span>
            <br />
            <span style={{ color: '#1f2937' }}>Beauty Vision</span>
          </h1>

          <p style={{ fontSize: '1.25rem', color: '#6b7280', marginBottom: '2rem', maxWidth: '48rem', margin: '0 auto 2rem auto', lineHeight: '1.6' }}>
            Create hyper-realistic, celebrity-worthy nail art and hairstyles with our
            cutting-edge AI. From elegant to trendy, bring your beauty dreams to life.
          </p>

          <div style={{
            display: 'flex',
            gap: '1rem',
            justifyContent: 'center',
            marginBottom: '3rem',
            flexWrap: 'wrap',
            position: 'relative',
            zIndex: 1000
          }}>
            <button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('🎨 AI Nail Art Studio button clicked!');
                setCurrentPage('nail-art');
              }}
              className="btn-primary"
              style={{
                fontSize: '1.125rem',
                padding: '1rem 2rem',
                cursor: 'pointer',
                pointerEvents: 'auto',
                position: 'relative',
                zIndex: 1001,
                border: 'none',
                outline: 'none'
              }}
            >
              🎨 AI Nail Art Studio
            </button>
            <button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('💅 View Pricing button clicked!');
                setCurrentPage('pricing');
              }}
              className="btn-secondary"
              style={{
                fontSize: '1.125rem',
                padding: '1rem 2rem',
                cursor: 'pointer',
                pointerEvents: 'auto',
                position: 'relative',
                zIndex: 1001,
                border: 'none',
                outline: 'none'
              }}
            >
              💅 View Pricing
            </button>
            <button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('Try Demo button clicked!');
                setCurrentPage('demo');
              }}
              className="btn-secondary"
              style={{
                fontSize: '1.125rem',
                padding: '1rem 2rem',
                cursor: 'pointer',
                pointerEvents: 'auto',
                position: 'relative',
                zIndex: 1001,
                border: 'none',
                outline: 'none'
              }}
            >
              Try Demo
            </button>
          </div>

          <div className="card-glass" style={{ padding: '2rem', maxWidth: '32rem', margin: '0 auto' }}>
            <div className="grid" style={{ gridTemplateColumns: 'repeat(2, 1fr)', gap: '1.5rem' }}>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                <div style={{
                  height: '8rem',
                  background: 'linear-gradient(135deg, #fce7f3 0%, #e9d5ff 100%)',
                  borderRadius: '0.5rem',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <span style={{ color: '#6b7280', fontWeight: '500' }}>Nail Art Preview</span>
                </div>
                <div style={{
                  height: '5rem',
                  background: 'linear-gradient(135deg, #e9d5ff 0%, #ddd6fe 100%)',
                  borderRadius: '0.5rem',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <span style={{ color: '#6b7280', fontSize: '0.875rem' }}>Style Options</span>
                </div>
              </div>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                <div style={{
                  height: '8rem',
                  background: 'linear-gradient(135deg, #ddd6fe 0%, #fce7f3 100%)',
                  borderRadius: '0.5rem',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <span style={{ color: '#6b7280', fontWeight: '500' }}>Hairstyle Preview</span>
                </div>
                <div style={{
                  height: '5rem',
                  background: 'linear-gradient(135deg, #fce7f3 0%, #e9d5ff 100%)',
                  borderRadius: '0.5rem',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <span style={{ color: '#6b7280', fontSize: '0.875rem' }}>Customization</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

// Simple Hairstyles Page
const HairstylesPage = () => {
  return (
    <div className="pt-16" style={{ padding: '4rem 1rem' }}>
      <div className="container">
        <div className="text-center mb-12">
          <h1 style={{ fontSize: '2.5rem', fontFamily: 'Playfair Display, serif', fontWeight: 'bold', marginBottom: '1.5rem' }}>
            <span className="gradient-text">AI Hairstyle Studio</span>
          </h1>
          <p style={{ fontSize: '1.25rem', color: '#6b7280', marginBottom: '2rem', maxWidth: '48rem', margin: '0 auto 2rem auto', lineHeight: '1.6' }}>
            Transform your look with AI-powered hairstyle generation and preview technology
          </p>
        </div>

        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
          gap: '2rem',
          maxWidth: '1200px',
          margin: '0 auto'
        }}>
          {/* Hairstyle Preview */}
          <div className="card-glass" style={{ padding: '2rem' }}>
            <h3 style={{ fontSize: '1.5rem', fontWeight: '600', color: '#374151', marginBottom: '1rem' }}>
              💇‍♀️ Hairstyle Preview
            </h3>
            <div style={{
              height: '300px',
              background: 'linear-gradient(135deg, #8b5cf6, #ec4899)',
              borderRadius: '1rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontSize: '3rem',
              marginBottom: '1rem'
            }}>
              💇‍♀️
            </div>
            <p style={{ color: '#6b7280', fontSize: '0.875rem' }}>
              Coming Soon: AI-powered hairstyle generation and virtual try-on technology
            </p>
          </div>

          {/* Style Options */}
          <div className="card-glass" style={{ padding: '2rem' }}>
            <h3 style={{ fontSize: '1.5rem', fontWeight: '600', color: '#374151', marginBottom: '1rem' }}>
              🎨 Hair Styles
            </h3>
            <div style={{ display: 'grid', gap: '1rem' }}>
              {['Bob Cut', 'Pixie Cut', 'Long Waves', 'Updo', 'Braids', 'Curls'].map((style, index) => (
                <div key={index} style={{
                  padding: '1rem',
                  background: 'rgba(139, 92, 246, 0.1)',
                  borderRadius: '0.5rem',
                  border: '1px solid rgba(139, 92, 246, 0.2)',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease'
                }}>
                  {style}
                </div>
              ))}
            </div>
          </div>

          {/* Customization */}
          <div className="card-glass" style={{ padding: '2rem' }}>
            <h3 style={{ fontSize: '1.5rem', fontWeight: '600', color: '#374151', marginBottom: '1rem' }}>
              ⚙️ Customization
            </h3>
            <div style={{ display: 'grid', gap: '1rem' }}>
              {['Hair Color', 'Length', 'Texture', 'Volume', 'Highlights', 'Style'].map((option, index) => (
                <div key={index} style={{
                  padding: '1rem',
                  background: 'rgba(16, 185, 129, 0.1)',
                  borderRadius: '0.5rem',
                  border: '1px solid rgba(16, 185, 129, 0.2)'
                }}>
                  {option}
                </div>
              ))}
            </div>
          </div>
        </div>

        <div style={{ textAlign: 'center', marginTop: '3rem' }}>
          <p style={{ color: '#6b7280', fontSize: '1rem', marginBottom: '1rem' }}>
            🚀 Full hairstyle AI features coming soon!
          </p>
          <button
            onClick={() => window.location.reload()}
            className="btn-primary"
            style={{ fontSize: '1rem', padding: '0.75rem 1.5rem' }}
          >
            Back to Home
          </button>
        </div>
      </div>
    </div>
  );
};

// Simple Demo Page
const DemoPage = () => {
  // Add CSS animation for spinning
  React.useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
      }
    `;
    document.head.appendChild(style);
    return () => document.head.removeChild(style);
  }, []);
  const [formData, setFormData] = useState({
    nailShape: 'oval',
    nailColor: 'nude',
    hairStyle: 'waves',
    vibe: 'elegant'
  });
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedImage, setGeneratedImage] = useState<string | null>(null);
  const [generationError, setGenerationError] = useState<string | null>(null);

  const handleGenerate = async () => {
    setIsGenerating(true);
    setGenerationError(null);
    setGeneratedImage(null);

    try {
      // Create a prompt based on the selected options
      const colorMap: { [key: string]: string } = {
        'nude': 'nude beige',
        'red': 'classic red',
        'pink': 'soft pink',
        'black': 'elegant black',
        'white': 'pure white',
        'burgundy': 'deep burgundy',
        'coral': 'coral pink',
        'lavender': 'lavender purple'
      };

      const shapeMap: { [key: string]: string } = {
        'oval': 'oval shaped',
        'square': 'square shaped',
        'round': 'round shaped',
        'almond': 'almond shaped'
      };

      const nailColor = colorMap[formData.nailColor] || formData.nailColor;
      const nailShape = shapeMap[formData.nailShape] || formData.nailShape;

      const prompt = `Professional photograph of elegant female hands with ${nailColor} nail polish, ${nailShape} nails, perfect manicure, ${formData.vibe} style, high-quality studio lighting, clean white background, photorealistic, ultra-detailed, beauty photography`;

      console.log('Generating nail art with prompt:', prompt);

      // Generate the nail art
      const response = await generateCustomNailArt(
        prompt,
        [nailColor],
        `${formData.vibe} ${nailColor} ${nailShape} nails`
      );

      console.log('Generation started, task ID:', response.task_id);

      // Wait for generation to complete
      const images = await waitForTaskCompletion(response.task_id);
      console.log('Generation completed, images:', images);

      if (images && images.length > 0) {
        setGeneratedImage(images[0]);
        console.log('Generated image URL:', images[0]);
      } else {
        setGenerationError('No images were generated');
      }
    } catch (error) {
      console.error('Error generating nail art:', error);
      setGenerationError(error instanceof Error ? error.message : 'Generation failed');
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="pt-16" style={{ padding: '4rem 1rem' }}>
      <div className="container">
        <div className="text-center mb-12">
          <h1 style={{ fontSize: '2.5rem', fontFamily: 'Playfair Display, serif', fontWeight: 'bold', marginBottom: '1.5rem' }}>
            <span className="gradient-text">Try CeleVibe Demo</span>
          </h1>
          <p style={{ fontSize: '1.25rem', color: '#6b7280', maxWidth: '48rem', margin: '0 auto' }}>
            Experience the power of AI-generated beauty designs. Customize your preferences
            and watch as we create stunning nail art and hairstyles just for you.
          </p>
        </div>

        <div className="grid" style={{ gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '3rem' }}>
          <div className="card-glass" style={{ padding: '2rem' }}>
            <h2 style={{ fontSize: '1.5rem', fontFamily: 'Playfair Display, serif', fontWeight: 'bold', marginBottom: '1.5rem' }}>
              Customize Your Look
            </h2>

            <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
              <div>
                <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                  Nail Shape
                </label>
                <select
                  value={formData.nailShape}
                  onChange={(e) => setFormData({...formData, nailShape: e.target.value})}
                  style={{ width: '100%', padding: '0.75rem', border: '1px solid #d1d5db', borderRadius: '0.5rem' }}
                >
                  <option value="oval">Oval</option>
                  <option value="square">Square</option>
                  <option value="almond">Almond</option>
                </select>
              </div>

              <div>
                <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                  Nail Color
                </label>
                <select
                  value={formData.nailColor}
                  onChange={(e) => setFormData({...formData, nailColor: e.target.value})}
                  style={{ width: '100%', padding: '0.75rem', border: '1px solid #d1d5db', borderRadius: '0.5rem' }}
                >
                  <option value="nude">Nude</option>
                  <option value="red">Classic Red</option>
                  <option value="pink">Soft Pink</option>
                  <option value="black">Elegant Black</option>
                  <option value="white">Pure White</option>
                  <option value="burgundy">Deep Burgundy</option>
                  <option value="coral">Coral</option>
                  <option value="lavender">Lavender</option>
                </select>
              </div>

              <div>
                <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                  Hair Style
                </label>
                <select
                  value={formData.hairStyle}
                  onChange={(e) => setFormData({...formData, hairStyle: e.target.value})}
                  style={{ width: '100%', padding: '0.75rem', border: '1px solid #d1d5db', borderRadius: '0.5rem' }}
                >
                  <option value="waves">Waves</option>
                  <option value="straight">Straight</option>
                  <option value="curls">Curls</option>
                </select>
              </div>

              <button
                onClick={handleGenerate}
                disabled={isGenerating}
                className="btn-primary"
                style={{ width: '100%', padding: '1rem', fontSize: '1.125rem' }}
              >
                {isGenerating ? 'Generating...' : 'Generate My Look'}
              </button>
            </div>
          </div>

          <div className="card-glass" style={{ padding: '2rem' }}>
            <h2 style={{ fontSize: '1.5rem', fontFamily: 'Playfair Display, serif', fontWeight: 'bold', marginBottom: '1.5rem' }}>
              Your Generated Look
            </h2>

            {isGenerating ? (
              <div className="text-center" style={{ padding: '3rem 0' }}>
                <div style={{
                  width: '4rem',
                  height: '4rem',
                  border: '2px solid #ec4899',
                  borderTop: '2px solid transparent',
                  borderRadius: '50%',
                  margin: '0 auto 1rem auto',
                  animation: 'spin 1s linear infinite'
                }}></div>
                <p style={{ color: '#6b7280' }}>Creating your perfect look...</p>
                <p style={{ color: '#9ca3af', fontSize: '0.875rem', marginTop: '0.5rem' }}>
                  This may take 30-60 seconds
                </p>
              </div>
            ) : generationError ? (
              <div className="text-center" style={{ padding: '3rem 0' }}>
                <div style={{
                  padding: '2rem',
                  background: 'rgba(239, 68, 68, 0.1)',
                  borderRadius: '1rem',
                  border: '1px solid rgba(239, 68, 68, 0.2)',
                  marginBottom: '1rem'
                }}>
                  <p style={{ color: '#dc2626', marginBottom: '1rem' }}>❌ {generationError}</p>
                  <p style={{ color: '#6b7280', fontSize: '0.875rem' }}>
                    Please try again or check your connection.
                  </p>
                </div>
                <button
                  onClick={handleGenerate}
                  style={{
                    padding: '0.75rem 1.5rem',
                    background: 'linear-gradient(135deg, #ec4899, #be185d)',
                    color: 'white',
                    border: 'none',
                    borderRadius: '0.5rem',
                    fontWeight: '600',
                    cursor: 'pointer'
                  }}
                >
                  Try Again
                </button>
              </div>
            ) : generatedImage ? (
              <div className="text-center">
                <img
                  src={generatedImage}
                  alt="Generated Nail Art"
                  style={{
                    maxWidth: '100%',
                    maxHeight: '400px',
                    borderRadius: '1rem',
                    boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',
                    marginBottom: '1.5rem'
                  }}
                />
                <div style={{
                  background: 'rgba(236, 72, 153, 0.1)',
                  padding: '1rem',
                  borderRadius: '0.75rem',
                  marginBottom: '1.5rem'
                }}>
                  <p style={{ color: '#be185d', fontWeight: '600', marginBottom: '0.5rem' }}>
                    ✨ Your Custom Look
                  </p>
                  <p style={{ color: '#6b7280', fontSize: '0.875rem' }}>
                    {formData.nailShape.charAt(0).toUpperCase() + formData.nailShape.slice(1)} • {' '}
                    {formData.nailColor.charAt(0).toUpperCase() + formData.nailColor.slice(1)} • {' '}
                    {formData.vibe.charAt(0).toUpperCase() + formData.vibe.slice(1)}
                  </p>
                </div>
                <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center', flexWrap: 'wrap' }}>
                  <button
                    onClick={() => {
                      const link = document.createElement('a');
                      link.href = generatedImage;
                      link.download = 'celevibe-nail-art.jpg';
                      link.click();
                    }}
                    style={{
                      padding: '0.75rem 1.5rem',
                      background: 'linear-gradient(135deg, #ec4899, #be185d)',
                      color: 'white',
                      border: 'none',
                      borderRadius: '0.5rem',
                      fontWeight: '600',
                      cursor: 'pointer',
                      fontSize: '0.875rem'
                    }}
                  >
                    💾 Download
                  </button>
                  <button
                    onClick={() => {
                      setGeneratedImage(null);
                      setGenerationError(null);
                    }}
                    style={{
                      padding: '0.75rem 1.5rem',
                      background: '#6b7280',
                      color: 'white',
                      border: 'none',
                      borderRadius: '0.5rem',
                      fontWeight: '600',
                      cursor: 'pointer',
                      fontSize: '0.875rem'
                    }}
                  >
                    🎨 Generate New
                  </button>
                </div>
              </div>
            ) : (
              <div className="text-center" style={{ padding: '3rem 0' }}>
                <div style={{
                  width: '6rem',
                  height: '6rem',
                  background: 'linear-gradient(135deg, #fce7f3 0%, #e9d5ff 100%)',
                  borderRadius: '50%',
                  margin: '0 auto 1rem auto',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <span style={{ fontSize: '2rem' }}>✨</span>
                </div>
                <p style={{ color: '#6b7280' }}>
                  Customize your preferences and click "Generate My Look" to see your AI-created beauty design.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// Simple Footer
const Footer = () => {
  return (
    <footer style={{ background: 'linear-gradient(135deg, #831843 0%, #581c87 50%, #831843 100%)', color: 'white', padding: '3rem 1rem' }}>
      <div className="container text-center">
        <div style={{ marginBottom: '2rem' }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '0.5rem', marginBottom: '1rem' }}>
            <div style={{
              width: '2rem',
              height: '2rem',
              background: 'white',
              borderRadius: '0.5rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <span style={{ color: '#ec4899', fontWeight: 'bold', fontSize: '1.125rem' }}>C</span>
            </div>
            <span style={{ fontSize: '1.5rem', fontFamily: 'Playfair Display, serif', fontWeight: 'bold' }}>
              CeleVibe
            </span>
          </div>
          <p style={{ color: 'rgba(255, 255, 255, 0.8)', maxWidth: '32rem', margin: '0 auto' }}>
            Transform your beauty vision into reality with AI-powered nail art and hairstyle generation.
          </p>
        </div>
        <p style={{ color: 'rgba(255, 255, 255, 0.6)' }}>
          © 2024 CeleVibe. All rights reserved.
        </p>
      </div>
    </footer>
  );
};

export default App;
