import React, { useState } from 'react';

interface StyleOptionsProps {
  onStyleSelect: (style: string) => void;
  onColorsSelect: (colors: string[]) => void;
  selectedStyle?: string;
  selectedColors?: string[];
}

const StyleOptions: React.FC<StyleOptionsProps> = ({ 
  onStyleSelect, 
  onColorsSelect, 
  selectedStyle, 
  selectedColors = [] 
}) => {
  const [activeTab, setActiveTab] = useState<'styles' | 'colors'>('styles');

  const nailStyles = [
    {
      id: 'french-tips',
      name: 'French Tips',
      description: 'Classic white tips with nude base',
      icon: '✨',
      difficulty: 'Easy',
      time: '30 min'
    },
    {
      id: 'burgundy',
      name: 'Dark Burgundy',
      description: 'Deep burgundy with glossy finish',
      icon: '💅',
      difficulty: 'Easy',
      time: '25 min'
    },
    {
      id: 'gradient',
      name: 'Gradient Ombre',
      description: 'Smooth color transitions',
      icon: '🌈',
      difficulty: 'Medium',
      time: '45 min'
    },
    {
      id: 'floral',
      name: 'Floral Art',
      description: 'Delicate flower patterns',
      icon: '🌸',
      difficulty: 'Hard',
      time: '60 min'
    },
    {
      id: 'geometric',
      name: 'Geometric',
      description: 'Modern geometric patterns',
      icon: '🔷',
      difficulty: 'Medium',
      time: '40 min'
    },
    {
      id: 'marble',
      name: 'Marble Effect',
      description: 'Luxurious marble patterns',
      icon: '🤍',
      difficulty: 'Hard',
      time: '50 min'
    }
  ];

  const colorPalettes = [
    {
      name: 'Classic Elegance',
      colors: ['#FFFFFF', '#FFB6C1', '#F5F5DC', '#E6E6FA']
    },
    {
      name: 'Bold & Beautiful',
      colors: ['#DC143C', '#FF1493', '#FF6347', '#FF4500']
    },
    {
      name: 'Deep Burgundy',
      colors: ['#4A1A3D', '#6B2C5A', '#800020', '#722F37']
    },
    {
      name: 'Ocean Blues',
      colors: ['#4682B4', '#5F9EA0', '#87CEEB', '#B0E0E6']
    },
    {
      name: 'Earth Tones',
      colors: ['#D2691E', '#CD853F', '#DEB887', '#F4A460']
    },
    {
      name: 'Pastel Dreams',
      colors: ['#FFB6C1', '#E0BBE4', '#FFDAB9', '#98FB98']
    },
    {
      name: 'Metallic Glam',
      colors: ['#FFD700', '#C0C0C0', '#B87333', '#E5E4E2']
    },
    {
      name: 'Neon Vibes',
      colors: ['#FF073A', '#39FF14', '#FF073A', '#FFFF33']
    }
  ];

  const handleColorPaletteSelect = (palette: string[]) => {
    onColorsSelect(palette);
  };

  const toggleColor = (color: string) => {
    const newColors = selectedColors.includes(color)
      ? selectedColors.filter(c => c !== color)
      : [...selectedColors, color];
    onColorsSelect(newColors);
  };

  return (
    <div style={{
      background: 'rgba(255, 255, 255, 0.9)',
      borderRadius: '1.5rem',
      padding: '2rem',
      boxShadow: '0 8px 24px rgba(0, 0, 0, 0.1)'
    }}>
      <h3 style={{
        fontSize: '1.5rem',
        fontWeight: '600',
        color: '#374151',
        marginBottom: '1.5rem',
        fontFamily: 'Playfair Display, serif',
        textAlign: 'center'
      }}>
        🎨 Style & Color Options
      </h3>

      {/* Tab Navigation */}
      <div style={{
        display: 'flex',
        marginBottom: '2rem',
        background: 'rgba(236, 72, 153, 0.1)',
        borderRadius: '0.75rem',
        padding: '0.25rem'
      }}>
        <button
          onClick={() => setActiveTab('styles')}
          style={{
            flex: 1,
            padding: '0.75rem',
            background: activeTab === 'styles' ? '#ec4899' : 'transparent',
            color: activeTab === 'styles' ? 'white' : '#6b7280',
            border: 'none',
            borderRadius: '0.5rem',
            fontWeight: '600',
            cursor: 'pointer',
            transition: 'all 0.3s ease'
          }}
        >
          💅 Nail Styles
        </button>
        <button
          onClick={() => setActiveTab('colors')}
          style={{
            flex: 1,
            padding: '0.75rem',
            background: activeTab === 'colors' ? '#ec4899' : 'transparent',
            color: activeTab === 'colors' ? 'white' : '#6b7280',
            border: 'none',
            borderRadius: '0.5rem',
            fontWeight: '600',
            cursor: 'pointer',
            transition: 'all 0.3s ease'
          }}
        >
          🎨 Colors
        </button>
      </div>

      {/* Styles Tab */}
      {activeTab === 'styles' && (
        <div>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '1rem',
            marginBottom: '1rem'
          }}>
            {nailStyles.map((style) => (
              <div
                key={style.id}
                onClick={() => onStyleSelect(style.id)}
                style={{
                  padding: '1.5rem',
                  border: selectedStyle === style.id ? '2px solid #ec4899' : '2px solid #e5e7eb',
                  borderRadius: '1rem',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  background: selectedStyle === style.id ? 'rgba(236, 72, 153, 0.1)' : 'white',
                  transform: selectedStyle === style.id ? 'scale(1.02)' : 'scale(1)'
                }}
              >
                <div style={{
                  fontSize: '2rem',
                  marginBottom: '0.5rem',
                  textAlign: 'center'
                }}>
                  {style.icon}
                </div>
                <h4 style={{
                  fontSize: '1.125rem',
                  fontWeight: '600',
                  color: '#374151',
                  marginBottom: '0.5rem',
                  textAlign: 'center'
                }}>
                  {style.name}
                </h4>
                <p style={{
                  fontSize: '0.875rem',
                  color: '#6b7280',
                  marginBottom: '1rem',
                  textAlign: 'center'
                }}>
                  {style.description}
                </p>
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  fontSize: '0.75rem',
                  color: '#9ca3af'
                }}>
                  <span>⏱️ {style.time}</span>
                  <span>📊 {style.difficulty}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Colors Tab */}
      {activeTab === 'colors' && (
        <div>
          <p style={{
            fontSize: '0.875rem',
            color: '#6b7280',
            marginBottom: '1.5rem',
            textAlign: 'center'
          }}>
            Choose from curated color palettes or select individual colors
          </p>

          {/* Color Palettes */}
          <div style={{ marginBottom: '2rem' }}>
            <h4 style={{
              fontSize: '1rem',
              fontWeight: '600',
              color: '#374151',
              marginBottom: '1rem'
            }}>
              🎨 Color Palettes
            </h4>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
              gap: '1rem'
            }}>
              {colorPalettes.map((palette, index) => (
                <div
                  key={index}
                  onClick={() => handleColorPaletteSelect(palette.colors)}
                  style={{
                    padding: '1rem',
                    border: '2px solid #e5e7eb',
                    borderRadius: '0.75rem',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    background: 'white'
                  }}
                >
                  <p style={{
                    fontSize: '0.875rem',
                    fontWeight: '600',
                    color: '#374151',
                    marginBottom: '0.75rem',
                    textAlign: 'center'
                  }}>
                    {palette.name}
                  </p>
                  <div style={{
                    display: 'flex',
                    gap: '0.25rem',
                    justifyContent: 'center'
                  }}>
                    {palette.colors.map((color, colorIndex) => (
                      <div
                        key={colorIndex}
                        style={{
                          width: '25px',
                          height: '25px',
                          backgroundColor: color,
                          borderRadius: '50%',
                          border: '2px solid white',
                          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)'
                        }}
                      />
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Individual Colors */}
          <div>
            <h4 style={{
              fontSize: '1rem',
              fontWeight: '600',
              color: '#374151',
              marginBottom: '1rem'
            }}>
              🌈 Individual Colors
            </h4>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill, minmax(40px, 1fr))',
              gap: '0.5rem',
              maxWidth: '400px'
            }}>
              {[
                '#FF0000', '#FF4500', '#FFD700', '#ADFF2F', '#00FF00', '#00CED1',
                '#0000FF', '#8A2BE2', '#FF1493', '#FF69B4', '#000000', '#FFFFFF',
                '#808080', '#C0C0C0', '#800000', '#808000', '#008000', '#800080',
                '#008080', '#000080', '#4A1A3D', '#6B2C5A', '#DC143C', '#FF6347'
              ].map((color, index) => (
                <div
                  key={index}
                  onClick={() => toggleColor(color)}
                  style={{
                    width: '40px',
                    height: '40px',
                    backgroundColor: color,
                    borderRadius: '50%',
                    border: selectedColors.includes(color) ? '3px solid #ec4899' : '2px solid white',
                    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    transform: selectedColors.includes(color) ? 'scale(1.1)' : 'scale(1)'
                  }}
                  title={color}
                />
              ))}
            </div>
          </div>

          {/* Selected Colors Display */}
          {selectedColors.length > 0 && (
            <div style={{
              marginTop: '1.5rem',
              padding: '1rem',
              background: 'rgba(236, 72, 153, 0.1)',
              borderRadius: '0.75rem'
            }}>
              <p style={{
                fontSize: '0.875rem',
                fontWeight: '600',
                color: '#374151',
                marginBottom: '0.75rem'
              }}>
                Selected Colors ({selectedColors.length}):
              </p>
              <div style={{
                display: 'flex',
                gap: '0.5rem',
                flexWrap: 'wrap'
              }}>
                {selectedColors.map((color, index) => (
                  <div
                    key={index}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.5rem',
                      background: 'white',
                      padding: '0.25rem 0.75rem',
                      borderRadius: '1rem',
                      fontSize: '0.75rem'
                    }}
                  >
                    <div
                      style={{
                        width: '20px',
                        height: '20px',
                        backgroundColor: color,
                        borderRadius: '50%',
                        border: '1px solid #e5e7eb'
                      }}
                    />
                    {color}
                    <button
                      onClick={() => toggleColor(color)}
                      style={{
                        background: 'none',
                        border: 'none',
                        color: '#6b7280',
                        cursor: 'pointer',
                        fontSize: '0.875rem'
                      }}
                    >
                      ×
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default StyleOptions;
